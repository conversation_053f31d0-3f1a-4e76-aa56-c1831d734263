#!/usr/bin/env python3
"""
Test script for critical fixes in the Integrated Cargo Parser.

This script validates:
1. AWB-level aggregation during XFFM processing
2. Processing order enforcement (XFFM before XFWB)
3. Unified suffix generation consistency
"""

import sys
import os
import logging
import json
from datetime import datetime

# Add the current directory to the path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from integrated_cargo_parser import IntegratedCargoParser
from services.unified_suffix_service import UnifiedSuffixService

def setup_logging():
    """Setup detailed logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_critical_fixes.log')
        ]
    )
    return logging.getLogger('CriticalFixesTest')

def test_awb_aggregation():
    """Test AWB-level aggregation functionality."""
    logger = logging.getLogger('AWBAggregationTest')
    logger.info("=" * 60)
    logger.info("TESTING AWB-LEVEL AGGREGATION")
    logger.info("=" * 60)
    
    try:
        with IntegratedCargoParser(branch_id=1, user_id=1, log_level=logging.INFO) as parser:
            # Test with a sample XFFM file that has multiple ULDs with same AWB
            test_files = [
                '/var/www/aircargomis/python/xml-parsers/test_data/sample_xffm_multi_uld.xml',
                '/var/www/aircargomis/python/xml-parsers/test_data/sample_xffm.xml'
            ]
            
            for test_file in test_files:
                if os.path.exists(test_file):
                    logger.info(f"Testing AWB aggregation with file: {test_file}")
                    result = parser.process_file(test_file)
                    
                    logger.info(f"Processing result: {json.dumps(result, indent=2, default=str)}")
                    
                    if result['success']:
                        logger.info("✅ AWB aggregation test PASSED")
                        logger.info(f"   - AWBs processed: {result.get('awb_count', 0)}")
                        logger.info(f"   - Partials created: {result.get('partial_count', 0)}")
                    else:
                        logger.error("❌ AWB aggregation test FAILED")
                        logger.error(f"   - Errors: {result.get('errors', [])}")
                    
                    break
            else:
                logger.warning("No test XFFM files found - creating sample data for testing")
                
    except Exception as e:
        logger.error(f"AWB aggregation test failed with exception: {str(e)}")

def test_processing_order():
    """Test processing order enforcement."""
    logger = logging.getLogger('ProcessingOrderTest')
    logger.info("=" * 60)
    logger.info("TESTING PROCESSING ORDER ENFORCEMENT")
    logger.info("=" * 60)
    
    try:
        with IntegratedCargoParser(branch_id=1, user_id=1, log_level=logging.INFO) as parser:
            # Test 1: Try to process XFWB before XFFM (should fail)
            logger.info("Test 1: Processing XFWB before XFFM (should be rejected)")
            
            test_xfwb = '/var/www/aircargomis/python/xml-parsers/test_data/sample_xfwb.xml'
            if os.path.exists(test_xfwb):
                result = parser.process_file(test_xfwb)
                
                if not result['success'] and result.get('processing_order_violation'):
                    logger.info("✅ Processing order enforcement PASSED")
                    logger.info(f"   - Correctly rejected XFWB: {result.get('error')}")
                else:
                    logger.error("❌ Processing order enforcement FAILED")
                    logger.error("   - XFWB was processed without XFFM first")
            
            # Test 2: Process XFFM first, then XFWB (should succeed)
            logger.info("Test 2: Processing XFFM first, then XFWB (should succeed)")
            
            test_xffm = '/var/www/aircargomis/python/xml-parsers/test_data/sample_xffm.xml'
            if os.path.exists(test_xffm):
                # Process XFFM first
                xffm_result = parser.process_file(test_xffm)
                logger.info(f"XFFM result: {xffm_result.get('success', False)}")
                
                if xffm_result['success'] and os.path.exists(test_xfwb):
                    # Now try XFWB
                    xfwb_result = parser.process_file(test_xfwb)
                    
                    if xfwb_result['success']:
                        logger.info("✅ Sequential processing PASSED")
                        logger.info("   - XFFM processed successfully, then XFWB processed successfully")
                    else:
                        logger.error("❌ Sequential processing FAILED")
                        logger.error(f"   - XFWB failed after XFFM: {xfwb_result.get('error')}")
                        
    except Exception as e:
        logger.error(f"Processing order test failed with exception: {str(e)}")

def test_suffix_generation():
    """Test unified suffix generation."""
    logger = logging.getLogger('SuffixGenerationTest')
    logger.info("=" * 60)
    logger.info("TESTING UNIFIED SUFFIX GENERATION")
    logger.info("=" * 60)
    
    try:
        from config.database import DB_CONFIG
        import psycopg2
        
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Initialize unified suffix service
        suffix_service = UnifiedSuffixService(conn, cursor, branch_id=1)
        
        # Test suffix generation
        test_awb = "TEST123456789"
        test_manifest = "TEST-MANIFEST-001"
        test_flight = "TEST001"
        test_date = "2024-01-01"
        
        logger.info(f"Testing suffix generation for AWB: {test_awb}")
        
        # Generate first suffix
        suffix1 = suffix_service.generate_suffix(
            test_awb, test_manifest, test_flight, test_date, "P"
        )
        logger.info(f"First suffix: {suffix1}")
        
        # Generate second suffix for different manifest
        suffix2 = suffix_service.generate_suffix(
            test_awb, "TEST-MANIFEST-002", test_flight, test_date, "P"
        )
        logger.info(f"Second suffix: {suffix2}")
        
        # Try to generate suffix for same manifest (should return existing)
        suffix3 = suffix_service.generate_suffix(
            test_awb, test_manifest, test_flight, test_date, "P"
        )
        logger.info(f"Third suffix (should match first): {suffix3}")
        
        if suffix1 == suffix3 and suffix1 != suffix2:
            logger.info("✅ Suffix generation consistency PASSED")
            logger.info("   - Same manifest returns same suffix")
            logger.info("   - Different manifest generates new suffix")
        else:
            logger.error("❌ Suffix generation consistency FAILED")
            logger.error(f"   - Expected {suffix1} == {suffix3}, got different values")
        
        # Get all suffixes for AWB
        all_suffixes = suffix_service.get_awb_suffixes(test_awb)
        logger.info(f"All suffixes for {test_awb}: {len(all_suffixes)} found")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Suffix generation test failed with exception: {str(e)}")

def test_directory_processing():
    """Test directory processing with correct file ordering."""
    logger = logging.getLogger('DirectoryProcessingTest')
    logger.info("=" * 60)
    logger.info("TESTING DIRECTORY PROCESSING WITH FILE ORDERING")
    logger.info("=" * 60)
    
    try:
        with IntegratedCargoParser(branch_id=1, user_id=1, log_level=logging.INFO) as parser:
            test_dir = '/var/www/aircargomis/python/xml-parsers/test_data'
            
            if os.path.exists(test_dir):
                logger.info(f"Processing directory: {test_dir}")
                result = parser.process_directory(test_dir)
                
                logger.info(f"Directory processing result: {json.dumps(result, indent=2, default=str)}")
                
                if result['success']:
                    logger.info("✅ Directory processing PASSED")
                    logger.info(f"   - Total files: {result.get('total_files', 0)}")
                    logger.info(f"   - Processed: {result.get('processed_files', 0)}")
                    logger.info(f"   - Failed: {result.get('failed_files', 0)}")
                else:
                    logger.error("❌ Directory processing FAILED")
                    logger.error(f"   - Errors: {result.get('errors', [])}")
            else:
                logger.warning(f"Test directory not found: {test_dir}")
                
    except Exception as e:
        logger.error(f"Directory processing test failed with exception: {str(e)}")

def main():
    """Run all critical fixes tests."""
    logger = setup_logging()
    
    logger.info("🚀 STARTING CRITICAL FIXES VALIDATION")
    logger.info(f"Test started at: {datetime.now()}")
    logger.info("=" * 80)
    
    # Run all tests
    test_awb_aggregation()
    test_processing_order()
    test_suffix_generation()
    test_directory_processing()
    
    logger.info("=" * 80)
    logger.info("🏁 CRITICAL FIXES VALIDATION COMPLETE")
    logger.info(f"Test completed at: {datetime.now()}")
    logger.info("Check test_critical_fixes.log for detailed results")

if __name__ == '__main__':
    main()
