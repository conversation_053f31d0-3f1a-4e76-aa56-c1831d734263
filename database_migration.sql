-- Enhanced Cargo Manifest Processing System Database Migration
-- This script adds the required tables and columns for the enhanced functionality

-- 1. Add missing columns to master_waybills
ALTER TABLE master_waybills ADD COLUMN IF NOT EXISTS processing_status VARCHAR(20) DEFAULT 'PENDING';
ALTER TABLE master_waybills ADD COLUMN IF NOT EXISTS total_pieces_declared INTEGER;
ALTER TABLE master_waybills ADD COLUMN IF NOT EXISTS gross_weight_expected DECIMAL(10,3);
ALTER TABLE master_waybills ADD COLUMN IF NOT EXISTS gross_weight_declared DECIMAL(10,3);
ALTER TABLE master_waybills ADD COLUMN IF NOT EXISTS volume_expected DECIMAL(10,3);
ALTER TABLE master_waybills ADD COLUMN IF NOT EXISTS volume_declared DECIMAL(10,3);

-- 2. Create duplicate_files table for file-level duplicate prevention
CREATE TABLE IF NOT EXISTS duplicate_files (
    id SERIAL PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    file_hash VARCHAR(64) UNIQUE NOT NULL,
    message_type VARCHAR(10) NOT NULL,
    file_path TEXT,
    file_size BIGINT,
    first_processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    attempt_count INTEGER DEFAULT 1,
    branch_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Create processing_logs table for enhanced logging
CREATE TABLE IF NOT EXISTS processing_logs (
    id SERIAL PRIMARY KEY,
    log_id VARCHAR(50) UNIQUE NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    message_type VARCHAR(10) NOT NULL,
    status VARCHAR(20) DEFAULT 'STARTED',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    duration_seconds INTEGER,
    awbs_processed INTEGER DEFAULT 0,
    ulds_processed INTEGER DEFAULT 0,
    errors_count INTEGER DEFAULT 0,
    error_message TEXT,
    processing_summary JSONB,
    branch_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. Create partial_suffix_tracking table for partial suffix management
CREATE TABLE IF NOT EXISTS partial_suffix_tracking (
    id SERIAL PRIMARY KEY,
    awb_number VARCHAR(20) NOT NULL,
    flight_number VARCHAR(10),
    flight_date DATE,
    manifest_id VARCHAR(50),
    suffix_number INTEGER NOT NULL,
    generated_suffix VARCHAR(30) NOT NULL,
    split_type VARCHAR(5),
    branch_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(awb_number, manifest_id)
);

-- 5. Enhance awb_hashes table if it exists
ALTER TABLE awb_hashes ADD COLUMN IF NOT EXISTS content_hash VARCHAR(64);
ALTER TABLE awb_hashes ADD COLUMN IF NOT EXISTS manifest_id VARCHAR(50);
ALTER TABLE awb_hashes ADD COLUMN IF NOT EXISTS source_file VARCHAR(255);
ALTER TABLE awb_hashes ADD COLUMN IF NOT EXISTS processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE awb_hashes ADD COLUMN IF NOT EXISTS branch_id INTEGER;
ALTER TABLE awb_hashes ADD COLUMN IF NOT EXISTS created_by INTEGER;
ALTER TABLE awb_hashes ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 6. Enhance partial_waybills table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'partial_waybills') THEN
        ALTER TABLE partial_waybills ADD COLUMN IF NOT EXISTS processing_status VARCHAR(20) DEFAULT 'AWAITING_CHECKIN';
        ALTER TABLE partial_waybills ADD COLUMN IF NOT EXISTS manifest_expected_pieces INTEGER;
        ALTER TABLE partial_waybills ADD COLUMN IF NOT EXISTS manifest_expected_weight DECIMAL(10,3);
        ALTER TABLE partial_waybills ADD COLUMN IF NOT EXISTS manifest_expected_volume DECIMAL(10,3);
        ALTER TABLE partial_waybills ADD COLUMN IF NOT EXISTS partial_suffix VARCHAR(30);
        ALTER TABLE partial_waybills ADD COLUMN IF NOT EXISTS content_hash VARCHAR(64);
    END IF;
END $$;

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_duplicate_files_hash ON duplicate_files(file_hash);
CREATE INDEX IF NOT EXISTS idx_duplicate_files_message_type ON duplicate_files(message_type);
CREATE INDEX IF NOT EXISTS idx_processing_logs_status ON processing_logs(status);
CREATE INDEX IF NOT EXISTS idx_processing_logs_message_type ON processing_logs(message_type);
CREATE INDEX IF NOT EXISTS idx_partial_suffix_awb ON partial_suffix_tracking(awb_number);
CREATE INDEX IF NOT EXISTS idx_master_waybills_processing_status ON master_waybills(processing_status);
CREATE INDEX IF NOT EXISTS idx_awb_hashes_content ON awb_hashes(content_hash);

-- 8. Update existing records with default processing status
UPDATE master_waybills SET processing_status = 'PENDING' WHERE processing_status IS NULL;

COMMIT;
