#!/usr/bin/env python3
"""
Debug script for XFFM processing issues.

This script helps debug why only flight_manifests are being inserted
and not master_waybills or other records.
"""

import sys
import os
import logging
from datetime import datetime

# Add the current directory to the path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_debug_logging():
    """Setup detailed debug logging."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('debug_xffm_processing.log')
        ]
    )
    return logging.getLogger('XFFMDebug')

def debug_database_connection():
    """Test database connection and check table structure."""
    logger = logging.getLogger('DatabaseDebug')

    try:
        from config.database import DB_CONFIG
        import psycopg2

        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        logger.info("✅ Database connection successful")

        # Check if required tables exist
        tables_to_check = ['flight_manifests', 'master_waybills', 'partial_waybills', 'uld_details', 'uld_awbs']

        for table in tables_to_check:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = %s
                );
            """, (table,))

            exists = cursor.fetchone()[0]
            if exists:
                logger.info(f"✅ Table {table} exists")

                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                logger.info(f"   - Current row count: {count}")
            else:
                logger.error(f"❌ Table {table} does not exist")

        conn.close()
        return True

    except Exception as e:
        logger.error(f"❌ Database connection failed: {str(e)}")
        return False

def debug_xffm_extraction(file_path):
    """Debug XFFM file extraction."""
    logger = logging.getLogger('ExtractionDebug')

    try:
        from extractors.xffm_extractor import XFFMExtractor

        extractor = XFFMExtractor(logger)
        result = extractor.extract_from_file(file_path)

        if result and result.get('success'):
            data = result.get('data', {})
            logger.info("✅ XFFM extraction successful")
            logger.info(f"   - Manifest ID: {data.get('manifest_id')}")
            logger.info(f"   - Flight info: {data.get('flight_info', {})}")

            ulds_data = data.get('ulds_data', [])
            logger.info(f"   - ULDs found: {len(ulds_data)}")

            total_awbs = 0
            for uld in ulds_data:
                awbs = uld.get('awbs', [])
                total_awbs += len(awbs)
                logger.info(f"     - ULD {uld.get('uld_id')}: {len(awbs)} AWBs")

                for awb in awbs[:2]:  # Show first 2 AWBs for debugging
                    logger.info(f"       - AWB: {awb.get('awb_number')} ({awb.get('pieces', 0)} pieces, {awb.get('weight', 0)} weight)")

            logger.info(f"   - Total AWBs: {total_awbs}")
            return data
        else:
            logger.error("❌ XFFM extraction failed")
            logger.error(f"   - Error: {result.get('error') if result else 'No result returned'}")
            return None

    except Exception as e:
        logger.error(f"❌ XFFM extraction exception: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

def debug_xffm_processing(file_path):
    """Debug full XFFM processing."""
    logger = logging.getLogger('ProcessingDebug')

    try:
        from integrated_cargo_parser import IntegratedCargoParser

        logger.info(f"🚀 Starting XFFM processing debug for: {file_path}")

        with IntegratedCargoParser(branch_id=1, user_id=1, log_level=logging.DEBUG) as parser:
            result = parser.process_file(file_path)

            logger.info("📊 Processing Result:")
            logger.info(f"   - Success: {result.get('success', False)}")
            logger.info(f"   - AWB Count: {result.get('awb_count', 0)}")
            logger.info(f"   - Partial Count: {result.get('partial_count', 0)}")
            logger.info(f"   - Processing Time: {result.get('processing_time_ms', 0)}ms")

            if result.get('errors'):
                logger.error("❌ Processing Errors:")
                for error in result['errors']:
                    logger.error(f"   - {error}")

            if result.get('warnings'):
                logger.warning("⚠️ Processing Warnings:")
                for warning in result['warnings']:
                    logger.warning(f"   - {warning}")

            # Check database after processing
            logger.info("🔍 Checking database after processing...")
            check_database_after_processing()

            return result

    except Exception as e:
        logger.error(f"❌ XFFM processing exception: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

def check_database_after_processing():
    """Check database state after processing."""
    logger = logging.getLogger('PostProcessingCheck')

    try:
        from config.database import DB_CONFIG
        import psycopg2

        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Check recent records in each table
        tables_to_check = [
            ('flight_manifests', 'created_at'),
            ('master_waybills', 'created_at'),
            ('partial_waybills', 'created_at'),
            ('uld_details', 'created_at'),
            ('uld_awbs', 'created_at')
        ]

        for table, date_column in tables_to_check:
            try:
                cursor.execute(f"""
                    SELECT COUNT(*) FROM {table}
                    WHERE {date_column} >= NOW() - INTERVAL '1 hour'
                """)
                recent_count = cursor.fetchone()[0]

                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                total_count = cursor.fetchone()[0]

                logger.info(f"📋 {table}: {recent_count} recent records, {total_count} total")

                if recent_count > 0 and table == 'master_waybills':
                    # Show recent master waybills
                    cursor.execute(f"""
                        SELECT awb_number, total_pieces, total_weight, status
                        FROM {table}
                        WHERE {date_column} >= NOW() - INTERVAL '1 hour'
                        ORDER BY {date_column} DESC
                        LIMIT 5
                    """)
                    recent_records = cursor.fetchall()
                    for record in recent_records:
                        logger.info(f"   - AWB: {record[0]}, Pieces: {record[1]}, Weight: {record[2]}, Status: {record[3]}")

            except Exception as table_error:
                logger.error(f"❌ Error checking {table}: {str(table_error)}")

        conn.close()

    except Exception as e:
        logger.error(f"❌ Database check failed: {str(e)}")

def main():
    """Main debug function."""
    logger = setup_debug_logging()

    logger.info("🔧 XFFM Processing Debug Started")
    logger.info(f"Debug started at: {datetime.now()}")
    logger.info("=" * 80)

    # Step 1: Check database connection
    logger.info("Step 1: Checking database connection...")
    if not debug_database_connection():
        logger.error("❌ Database connection failed - stopping debug")
        return

    # Step 2: Find XFFM file to test
    test_files = [
        '/var/www/aircargomis/python/xml-parsers/test_data/sample_xffm.xml',
        '/var/www/aircargomis/examples/sample_xffm.xml',
        # Add more potential test file paths here
    ]

    test_file = None
    for file_path in test_files:
        if os.path.exists(file_path):
            test_file = file_path
            break

    if not test_file:
        logger.error("❌ No test XFFM file found")
        logger.info("Please provide a test XFFM file path as argument:")
        logger.info("python debug_xffm_processing.py /path/to/test.xml")
        return

    logger.info(f"📁 Using test file: {test_file}")

    # Step 3: Debug extraction
    logger.info("Step 3: Testing XFFM extraction...")
    extracted_data = debug_xffm_extraction(test_file)
    if not extracted_data:
        logger.error("❌ Extraction failed - stopping debug")
        return

    # Step 4: Debug full processing
    logger.info("Step 4: Testing full XFFM processing...")
    processing_result = debug_xffm_processing(test_file)

    logger.info("=" * 80)
    logger.info("🏁 XFFM Processing Debug Complete")
    logger.info(f"Debug completed at: {datetime.now()}")
    logger.info("Check debug_xffm_processing.log for detailed results")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        # Use provided file path
        test_file = sys.argv[1]
        if os.path.exists(test_file):
            logger = setup_debug_logging()
            logger.info(f"Using provided test file: {test_file}")
            debug_xffm_processing(test_file)
        else:
            print(f"File not found: {test_file}")
    else:
        main()
