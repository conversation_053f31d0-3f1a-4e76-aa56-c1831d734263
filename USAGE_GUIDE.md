# 📋 XML Parsers Complete Usage Guide

## 🚀 Quick Start

### 1. **How to Run the XML Parsers**

#### **A. Using the Unified Parser Interface (Recommended)**
```bash
cd /var/www/cargo-mis/python/xml-parsers
source venv/bin/activate

# Auto-detect parser type from XML content
./run_parser.py -f path/to/file.xml

# Process a directory of files
./run_parser.py -d path/to/directory

# Specify parser type explicitly
./run_parser.py -f path/to/file.xml -t xfwb

# With branch and user IDs
./run_parser.py -f path/to/file.xml -b 1 -u 1 -v

# Save results to JSON file
./run_parser.py -f path/to/file.xml -o results.json
```

#### **B. Using Individual Parser Scripts**
```bash
# XFFM (Flight Manifest)
python process_xffm.py path/to/file.xml [branch_id] [user_id]

# XFWB (Waybill) 
python process_xfwb.py path/to/file.xml [branch_id] [user_id]

# XFZB (House Waybill)
python process_xfzb.py path/to/file.xml [branch_id] [user_id]
```

### 2. **Configuration**

#### **Database Configuration**
- ✅ **Already configured** in `config/database.py`
- Database: `cargo_handling_system`
- User: `cargo_user`
- Host: `127.0.0.1:5432`

#### **Virtual Environment**
- ✅ **Already set up** in `venv/`
- Dependencies installed: `lxml`, `psycopg2-binary`, `watchdog`

### 3. **Testing the Parsers**

#### **Test Auto-Detection**
```bash
source venv/bin/activate
python demo_auto_detection.py
```

#### **Test Individual Parsers**
```bash
# Test with help
./run_parser.py --help

# Test with verbose output
./run_parser.py -f sample.xml -v
```

### 4. **Automatic Service Setup**

#### **Install and Start the Monitor Service**
```bash
# Run the setup script
sudo ./setup-service.sh

# Start the service
sudo systemctl start xml-parser-monitor

# Check status
sudo systemctl status xml-parser-monitor
```

#### **Service Management**
```bash
# Start service
sudo systemctl start xml-parser-monitor

# Stop service
sudo systemctl stop xml-parser-monitor

# Restart service
sudo systemctl restart xml-parser-monitor

# Check status
sudo systemctl status xml-parser-monitor

# View logs
sudo journalctl -u xml-parser-monitor -f

# View service logs
tail -f /var/log/xml-parser-monitor.log
```

## 📁 **Directory Structure**

```
/var/www/cargo-mis/storage/
├── xml-incoming/     # Drop XML files here for auto-processing
└── xml-processed/    # Processed files are moved here
```

## 🔧 **Advanced Usage**

### **Manual Monitor Service**
```bash
# Run monitor manually (for testing)
source venv/bin/activate
python xml-parser-monitor.py \
  --watch-dir /var/www/cargo-mis/storage/xml-incoming \
  --processed-dir /var/www/cargo-mis/storage/xml-processed \
  --branch-id 1 \
  --user-id 1 \
  --verbose
```

### **Batch Processing**
```bash
# Process all XML files in a directory
./run_parser.py -d /path/to/xml/files -v

# Process specific type only
./run_parser.py -d /path/to/xml/files -t xfwb -v
```

## 🔍 **Auto-Detection Rules**

- **XFFM (Flight Manifest)**: `TypeCode = 122` with specific attributes
- **XFWB (Air Waybill)**: `TypeCode = 741` (Master) or `740` (Direct)
- **XFZB (House Air Waybill)**: `TypeCode = 703`

## 📊 **Monitoring & Logs**

### **Log Files**
- Service logs: `/var/log/xml-parser-monitor.log`
- Parser logs: `xml_parser.log` (in working directory)

### **Status Checks**
```bash
# Check if service is running
systemctl is-active xml-parser-monitor

# Check service status
systemctl status xml-parser-monitor

# View recent logs
journalctl -u xml-parser-monitor --since "1 hour ago"
```

## 🚨 **Troubleshooting**

### **Common Issues**

1. **Service won't start**
   ```bash
   # Check logs
   sudo journalctl -u xml-parser-monitor -n 50
   
   # Check permissions
   ls -la /var/www/cargo-mis/storage/
   ```

2. **Database connection issues**
   ```bash
   # Test database connection
   source venv/bin/activate
   python -c "from config.database import DB_CONFIG; import psycopg2; psycopg2.connect(**DB_CONFIG); print('✅ Database connection OK')"
   ```

3. **Permission issues**
   ```bash
   # Fix permissions
   sudo chown -R cargo:cargo /var/www/cargo-mis/storage/
   sudo chmod 755 /var/www/cargo-mis/storage/xml-*
   ```

## 🎯 **Integration with Laravel**

The parsers are integrated with Laravel through the `XmlImportController`. The service automatically processes files dropped in the incoming directory.

### **Current Integration Points**
- Laravel calls individual scripts: `process_xffm.py`, `process_xfwb.py`, `process_xfzb.py`
- Database tables: `flight_manifests`, `waybills`, `house_waybills`, etc.

## 📈 **Performance Notes**

- Auto-detection adds minimal overhead (~1-2ms per file)
- Service processes files immediately when detected
- Processed files are timestamped and moved to avoid reprocessing
- Multiple files can be processed concurrently
