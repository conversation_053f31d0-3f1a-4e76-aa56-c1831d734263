# XML Message Type Auto-Detection

This document describes the automatic XML message type detection feature implemented in the IATA XML parsers.

## Overview

The XML parsers now support automatic detection of message types (XFFM, XFWB, XFZB) based on the `TypeCode` element values within the XML content. This eliminates the need to manually specify the parser type or rely solely on filename patterns.

## Detection Rules

The auto-detection system analyzes the `<ram:TypeCode>` element (or equivalent with different namespace prefixes) and applies the following rules:

### XFFM (Flight Manifest)
- **TypeCode**: `122`
- **Required Attributes**:
  - `listID="1001"`
  - `listAgencyID="6"`
  - `listVersionID="D09A"`
- **Example**:
  ```xml
  <ram:TypeCode listID="1001" listAgencyID="6" listVersionID="D09A">122</ram:TypeCode>
  ```

### XFWB (Air Waybill)
- **TypeCode**: `741` (Master with houses) OR `740` (Direct without houses)
- **Required Attributes**: None
- **Examples**:
  ```xml
  <ram:TypeCode>741</ram:TypeCode>  <!-- Master AWB that can contain house waybills -->
  <ram:TypeCode>740</ram:TypeCode>  <!-- Direct AWB without house waybills -->
  ```

### XFZB (House Air Waybill)
- **TypeCode**: `703`
- **Required Attributes**: None
- **Example**:
  ```xml
  <ram:TypeCode>703</ram:TypeCode>
  ```

## Namespace Handling

The detection system is namespace-agnostic and can handle various namespace prefixes:
- `ram:TypeCode`
- `ns0:TypeCode`
- `ns1:TypeCode`
- Any other prefix with `TypeCode` as the local name

## Implementation

### Core Components

1. **MessageTypeDetector Class** (`utils/message_type_detector.py`)
   - Standalone detector class
   - Methods for file, string, and element detection
   - Comprehensive error handling

2. **BaseParser Integration** (`parsers/base_parser.py`)
   - Auto-detection during parser initialization
   - Static methods for standalone detection
   - Automatic namespace configuration

3. **Run Parser Integration** (`run_parser.py`)
   - Auto-detection when no parser type specified
   - Fallback to filename-based detection
   - Enhanced error reporting

### Usage Examples

#### Command Line (Auto-Detection)
```bash
# Auto-detect from XML content
python run_parser.py -f sample.xml

# Process directory with auto-detection
python run_parser.py -d /path/to/xml/files

# Explicit type specification (bypasses auto-detection)
python run_parser.py -f sample.xml -t xfwb
```

#### Programmatic Usage
```python
from utils.message_type_detector import MessageTypeDetector
from parsers.base_parser import BaseParser

# Using MessageTypeDetector directly
detector = MessageTypeDetector()
message_type = detector.detect_from_file('sample.xml')
print(f"Detected type: {message_type}")

# Using BaseParser static methods
message_type = BaseParser.detect_message_type_from_file('sample.xml')
message_type = BaseParser.detect_message_type_from_string(xml_content)

# Auto-detection during BaseParser initialization
parser = BaseParser(xml_file='sample.xml')  # xml_type will be auto-detected
print(f"Auto-detected type: {parser.xml_type}")
```

## Testing

### Unit Tests
Run the comprehensive test suite:
```bash
python -m pytest test_message_type_detection.py -v
```

### Sample File Testing
Test with existing sample files:
```bash
python test_detection_samples.py
```

### Integration Testing
Test the complete workflow:
```bash
# Test auto-detection with various file types
python run_parser.py -f ../xmlmakerapp/samples/xffm/4Z460_19APR.xml --verbose
python run_parser.py -f ../xmlmakerapp/samples/xfwb/176-09650384-XFWB.xml --verbose
python run_parser.py -f ../xmlmakerapp/samples/xfzb/176-09650384-H03.xml --verbose
```

## Error Handling

The auto-detection system includes comprehensive error handling:

1. **XML Parsing Errors**: Gracefully handled with detailed logging
2. **Missing TypeCode**: Falls back to filename-based detection
3. **Unknown TypeCode**: Returns `None` with warning logs
4. **Invalid XFFM Attributes**: TypeCode 122 without proper attributes is rejected

## Performance

- **Minimal Overhead**: Detection uses lightweight XPath queries
- **Early Detection**: Stops at first matching TypeCode element
- **Caching**: BaseParser caches detection results
- **Fallback Strategy**: Quick filename-based fallback when needed

## Backward Compatibility

The auto-detection feature is fully backward compatible:
- Existing scripts continue to work unchanged
- Explicit type specification still supported and takes precedence
- Filename-based detection remains as fallback
- All existing APIs preserved

## Troubleshooting

### Common Issues

1. **Detection Returns None**
   - Check if XML contains valid TypeCode elements
   - Verify TypeCode values match expected patterns
   - Enable verbose logging for detailed analysis

2. **Wrong Type Detected**
   - Verify XML content matches expected message type
   - Check for multiple TypeCode elements in document
   - Review namespace usage in XML

3. **Performance Issues**
   - Large XML files may take longer to parse
   - Consider using filename-based detection for batch processing
   - Monitor memory usage with very large files

### Debug Logging

Enable verbose logging to see detection details:
```bash
python run_parser.py -f sample.xml --verbose
```

## Future Enhancements

Potential improvements for future versions:
- Support for additional message types
- Configurable detection rules
- Performance optimizations for large files
- Enhanced error reporting and suggestions
