#!/usr/bin/env python3
"""
XFFM data validator.

This module provides functionality for validating data extracted from XFFM XML files.
"""

import os
import sys

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from validators.base_validator import BaseValidator


class XFFMValidator(BaseValidator):
    """
    Validator for XFFM (Flight Manifest) XML data.

    This class validates all extracted data from XFFM XML files including
    flight information, ULD details, and waybill allocations.
    """

    def validate(self, data):
        """
        Validate extracted XFFM data.

        Args:
            data (dict): Extracted data to validate.

        Returns:
            bool: True if valid, False otherwise.
        """
        self.clear_errors()

        # Validate basic flight manifest information
        self.validate_manifest_info(data)

        # Validate flight information
        self.validate_flight_info(data)

        # Validate ULD information
        self.validate_uld_info(data)

        # Validate waybill information
        self.validate_waybill_info(data)

        # Validate business rules
        self.validate_business_rules(data)

        return not self.has_errors()

    def validate_manifest_info(self, data):
        """Validate basic manifest information."""
        # Manifest ID is required
        if not self.validate_required_field(data.get("manifest_id"), "manifest_id"):
            return

        # Validate manifest ID format
        manifest_id = data.get("manifest_id")
        if manifest_id:
            self.validate_string_length(
                manifest_id, "manifest_id", min_length=1, max_length=50
            )

            # Check if it has proper prefix
            if not (manifest_id.startswith("FM-") or manifest_id.startswith("MAN-")):
                self.add_warning(
                    "Manifest ID should start with 'FM-' or 'MAN-' prefix",
                    "manifest_id",
                )

    def validate_flight_info(self, data):
        """Validate flight information."""
        # Flight number is required
        if not self.validate_required_field(data.get("flight_number"), "flight_number"):
            return

        # Validate flight number format
        flight_number = data.get("flight_number")
        if flight_number:
            self.validate_string_length(
                flight_number, "flight_number", min_length=2, max_length=10
            )

            # Basic flight number format validation (2-3 letter carrier code + numbers)
            if not self.validate_pattern(
                flight_number,
                "flight_number",
                r"^[A-Z]{2,3}\d+[A-Z]?$",
                "flight number format (e.g., AA123, BA456A)",
            ):
                self.add_warning(
                    f"Flight number '{flight_number}' has unusual format",
                    "flight_number",
                )

        # Carrier code validation
        carrier_code = data.get("carrier_code")
        if carrier_code:
            self.validate_string_length(
                carrier_code, "carrier_code", min_length=2, max_length=3
            )
            if not carrier_code.isalpha():
                self.add_error("Carrier code must contain only letters", "carrier_code")
        else:
            self.add_warning("Carrier code not found", "carrier_code")

        # Airport codes validation
        self.validate_airport_code(data.get("departure_airport"), "departure_airport")
        self.validate_airport_code(data.get("arrival_airport"), "arrival_airport")

        # Check that departure and arrival airports are different
        if (
            data.get("departure_airport")
            and data.get("arrival_airport")
            and data.get("departure_airport") == data.get("arrival_airport")
        ):
            self.add_warning("Departure and arrival airports are the same", "airports")

        # Flight date validation
        flight_date = data.get("flight_date")
        if flight_date:
            if not self.validate_pattern(
                flight_date,
                "flight_date",
                r"^\d{4}-\d{2}-\d{2}$",
                "date format (YYYY-MM-DD)",
            ):
                self.add_error("Invalid flight date format", "flight_date")

        # Datetime validation
        self.validate_datetime_field(
            data.get("scheduled_departure"), "scheduled_departure"
        )
        self.validate_datetime_field(data.get("scheduled_arrival"), "scheduled_arrival")

        # Optional datetime validation
        if data.get("actual_departure"):
            self.validate_datetime_field(
                data.get("actual_departure"), "actual_departure"
            )
        if data.get("actual_arrival"):
            self.validate_datetime_field(data.get("actual_arrival"), "actual_arrival")

        # Total pieces and weight validation
        total_pieces = data.get("total_pieces", 0)
        total_weight = data.get("total_weight", 0.0)

        self.validate_numeric_range(
            total_pieces, "total_pieces", min_value=0, max_value=999999
        )
        self.validate_numeric_range(
            total_weight, "total_weight", min_value=0.0, max_value=999999.99
        )

    def validate_uld_info(self, data):
        """Validate ULD information."""
        ulds = data.get("ulds", [])

        if not isinstance(ulds, list):
            self.add_error("ULDs must be a list", "ulds")
            return

        if len(ulds) == 0:
            self.add_warning("No ULDs found in manifest", "ulds")
            return

        uld_ids = set()
        for i, uld in enumerate(ulds):
            if not isinstance(uld, dict):
                self.add_error(f"ULD at index {i} must be a dictionary", "ulds")
                continue

            # Validate ULD ID
            uld_id = uld.get("uld_id")
            if not uld_id:
                self.add_error(f"ULD at index {i} missing ULD ID", "ulds")
                continue

            # Check for duplicate ULD IDs
            if uld_id in uld_ids:
                self.add_error(f"Duplicate ULD ID: {uld_id}", "ulds")
            else:
                uld_ids.add(uld_id)

            # Validate ULD ID format
            self.validate_string_length(
                uld_id, f"uld_{i}_id", min_length=1, max_length=20
            )

            # Validate ULD type
            uld_type = uld.get("uld_type")
            if uld_type:
                self.validate_string_length(uld_type, f"uld_{i}_type", max_length=10)

            # Validate ULD owner
            uld_owner = uld.get("uld_owner")
            if uld_owner:
                self.validate_string_length(uld_owner, f"uld_{i}_owner", max_length=10)

            # Validate ULD weight and pieces
            uld_weight = uld.get("weight", 0.0)
            uld_pieces = uld.get("pieces", 0)

            self.validate_numeric_range(
                uld_weight, f"uld_{i}_weight", min_value=0.0, max_value=99999.99
            )
            self.validate_numeric_range(
                uld_pieces, f"uld_{i}_pieces", min_value=0, max_value=99999
            )

            # Validate AWBs in ULD
            awbs = uld.get("awbs", [])
            if not isinstance(awbs, list):
                self.add_error(f"ULD {uld_id} AWBs must be a list", "ulds")
            else:
                self.validate_uld_awbs(uld_id, awbs, i)

    def validate_uld_awbs(self, uld_id, awbs, uld_index):
        """Validate AWBs within a ULD."""
        awb_numbers = set()

        for j, awb in enumerate(awbs):
            if not isinstance(awb, dict):
                self.add_error(
                    f"AWB at index {j} in ULD {uld_id} must be a dictionary", "ulds"
                )
                continue

            # Validate AWB number
            awb_number = awb.get("awb_number")
            if not awb_number:
                self.add_error(
                    f"AWB at index {j} in ULD {uld_id} missing AWB number", "ulds"
                )
                continue

            # Track AWB numbers within ULD but don't treat duplicates as errors
            if awb_number in awb_numbers:
                self.add_warning(
                    f"AWB number {awb_number} appears multiple times in ULD {uld_id} (will be aggregated)",
                    "ulds",
                )
            awb_numbers.add(awb_number)

            # Validate AWB number format
            self.validate_awb_number(awb_number, f"uld_{uld_index}_awb_{j}_number")

            # Validate AWB pieces and weight
            awb_pieces = awb.get("pieces", 0)
            awb_weight = awb.get("weight", 0.0)

            self.validate_numeric_range(
                awb_pieces,
                f"uld_{uld_index}_awb_{j}_pieces",
                min_value=0,
                max_value=99999,
            )
            self.validate_numeric_range(
                awb_weight,
                f"uld_{uld_index}_awb_{j}_weight",
                min_value=0.0,
                max_value=99999.99,
            )

            # Validate weight unit
            weight_unit = awb.get("weight_unit")
            if weight_unit:
                self.validate_choice(
                    weight_unit, f"uld_{uld_index}_awb_{j}_weight_unit", ["KGM", "LBR"]
                )

            # Validate volume unit if volume is present
            gross_volume = awb.get("gross_volume")
            volume_unit = awb.get("volume_unit")

            if gross_volume is not None:
                self.validate_numeric_range(
                    gross_volume,
                    f"uld_{uld_index}_awb_{j}_volume",
                    min_value=0.001,
                    max_value=99999.999,
                )
                if volume_unit:
                    self.validate_choice(
                        volume_unit,
                        f"uld_{uld_index}_awb_{j}_volume_unit",
                        ["MC", "MTQ", "FTQ"],
                    )
            elif volume_unit is not None:
                self.add_warning(
                    f"Volume unit specified without volume for AWB {awb_number}", "ulds"
                )

    def validate_waybill_info(self, data):
        """Validate waybill information."""
        waybills = data.get("waybills", [])

        if not isinstance(waybills, list):
            self.add_error("Waybills must be a list", "waybills")
            return

        awb_numbers = set()
        for i, waybill in enumerate(waybills):
            if not isinstance(waybill, dict):
                self.add_error(f"Waybill at index {i} must be a dictionary", "waybills")
                continue

            # Validate AWB number
            awb_number = waybill.get("awb_number")
            if not awb_number:
                self.add_error(f"Waybill at index {i} missing AWB number", "waybills")
                continue

            # Handle AWB duplicates based on TransportSplitDescription codes
            split_code = waybill.get("transport_split_code")
            if awb_number in awb_numbers:
                if split_code == "S":
                    self.add_warning(
                        f"AWB {awb_number} split across ULDs (code: S) - will be aggregated",
                        "waybills",
                    )
                elif split_code == "P":
                    self.add_warning(
                        f"AWB {awb_number} is partial shipment (code: P) - will be aggregated",
                        "waybills",
                    )
                else:
                    self.add_warning(
                        f"AWB {awb_number} appears multiple times without split code - will be aggregated",
                        "waybills",
                    )
            awb_numbers.add(awb_number)

            # Validate AWB number format
            self.validate_awb_number(awb_number, f"waybill_{i}_number")

    def validate_business_rules(self, data):
        """Validate business rules and logical consistency."""
        # Check datetime consistency
        scheduled_departure = data.get("scheduled_departure")
        scheduled_arrival = data.get("scheduled_arrival")
        actual_departure = data.get("actual_departure")
        # actual_arrival = data.get("actual_arrival")  # Future use

        # Scheduled arrival should be after scheduled departure
        if scheduled_departure and scheduled_arrival:
            if scheduled_departure >= scheduled_arrival:
                self.add_warning(
                    "Scheduled arrival is not after scheduled departure", "flight_times"
                )

        # Actual times should be consistent with scheduled times
        if actual_departure and scheduled_departure:
            # Allow some flexibility but warn if very different
            pass  # Could add more sophisticated time validation here

        # ULD and waybill consistency
        ulds = data.get("ulds", [])
        waybills = data.get("waybills", [])

        # Collect all AWB numbers from ULDs
        uld_awb_numbers = set()
        uld_total_pieces = 0
        uld_total_weight = 0.0

        for uld in ulds:
            awbs = uld.get("awbs", [])
            for awb in awbs:
                awb_number = awb.get("awb_number")
                if awb_number:
                    uld_awb_numbers.add(awb_number)

            uld_total_pieces += uld.get("pieces", 0)
            uld_total_weight += uld.get("weight", 0.0)

        # Collect all AWB numbers from waybills
        waybill_awb_numbers = set()
        for waybill in waybills:
            awb_number = waybill.get("awb_number")
            if awb_number:
                waybill_awb_numbers.add(awb_number)

        # Check for AWBs in waybills but not in ULDs
        unallocated_awbs = waybill_awb_numbers - uld_awb_numbers
        if unallocated_awbs:
            self.add_warning(
                f"AWBs found in waybills but not allocated to ULDs: {', '.join(unallocated_awbs)}",
                "awb_allocation",
            )

        # Check for AWBs in ULDs but not in waybills
        orphaned_awbs = uld_awb_numbers - waybill_awb_numbers
        if orphaned_awbs:
            self.add_warning(
                f"AWBs allocated to ULDs but not found in waybills: {', '.join(orphaned_awbs)}",
                "awb_allocation",
            )

        # Check total pieces and weight consistency
        manifest_total_pieces = data.get("total_pieces", 0)
        manifest_total_weight = data.get("total_weight", 0.0)

        if manifest_total_pieces > 0 and uld_total_pieces > 0:
            if (
                abs(manifest_total_pieces - uld_total_pieces)
                > manifest_total_pieces * 0.1
            ):  # 10% tolerance
                self.add_warning(
                    f"Manifest total pieces ({manifest_total_pieces}) differs significantly from ULD total ({uld_total_pieces})",
                    "totals_consistency",
                )

        if manifest_total_weight > 0 and uld_total_weight > 0:
            if (
                abs(manifest_total_weight - uld_total_weight)
                > manifest_total_weight * 0.1
            ):  # 10% tolerance
                self.add_warning(
                    f"Manifest total weight ({manifest_total_weight}) differs significantly from ULD total ({uld_total_weight})",
                    "totals_consistency",
                )

    def validate_datetime_field(self, datetime_str, field_name):
        """Validate datetime field format."""
        if not datetime_str:
            return True

        # Check basic datetime format patterns
        datetime_patterns = [
            r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$",  # YYYY-MM-DD HH:MM:SS
            r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$",  # ISO format (exact)
            r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z?$",  # ISO format with optional Z
            r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$",  # ISO with timezone
        ]

        for pattern in datetime_patterns:
            if self.validate_pattern(
                datetime_str, field_name, pattern, "datetime format"
            ):
                return True

        self.add_warning(
            f"Datetime format for {field_name} is non-standard but accepted: {datetime_str}",
            field_name,
        )
        return True  # Accept non-standard formats with warning instead of error
