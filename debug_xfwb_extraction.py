#!/usr/bin/env python3

import sys
import os
from lxml import etree
from extractors.xfwb_extractor import XF<PERSON>BExtractor
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def debug_xfwb_extraction(file_path):
    """Debug XFWB AWB extraction"""
    
    print(f"=== DEBUGGING XFWB EXTRACTION FOR: {file_path} ===")
    
    # Test 1: Parse XML directly
    print("\n1. PARSING XML DIRECTLY:")
    try:
        parser = etree.XMLParser(remove_blank_text=True)
        tree = etree.parse(file_path, parser)
        root = tree.getroot()
        print(f"✅ XML parsed successfully")
        print(f"Root tag: {root.tag}")
        print(f"Root namespace: {root.nsmap}")
    except Exception as e:
        print(f"❌ XML parsing failed: {e}")
        return
    
    # Test 2: Manual AWB extraction
    print("\n2. MANUAL AWB EXTRACTION:")
    
    # Try different XPath patterns
    xpath_patterns = [
        ".//*[local-name()='MessageHeaderDocument']/*[local-name()='ID']",
        ".//*[local-name()='BusinessHeaderDocument']/*[local-name()='ID']",
        ".//*[local-name()='TransportContractDocument']/*[local-name()='ID']",
        ".//*[local-name()='MasterConsignment']/*[local-name()='ID']",
        "//ram:ID",
        ".//ram:ID",
        ".//*[local-name()='ID']",
        "//ID"
    ]
    
    for pattern in xpath_patterns:
        try:
            elements = root.xpath(pattern)
            if elements:
                for elem in elements[:3]:  # Show first 3 matches
                    if elem.text:
                        print(f"  {pattern}: '{elem.text.strip()}'")
            else:
                print(f"  {pattern}: No matches")
        except Exception as e:
            print(f"  {pattern}: Error - {e}")
    
    # Test 3: Use XFWB Extractor
    print("\n3. USING XFWB EXTRACTOR:")
    try:
        extractor = XFWBExtractor(logger)
        result = extractor.extract_from_file(file_path)
        print(f"Extraction result: {result}")
        
        if result and result.get('success'):
            master_waybill = result.get('master_waybill', {})
            awb_number = master_waybill.get('awb_number')
            print(f"✅ AWB found: {awb_number}")
        else:
            print(f"❌ Extraction failed")
            
    except Exception as e:
        print(f"❌ Extractor error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 4: Direct extract method
    print("\n4. USING DIRECT EXTRACT METHOD:")
    try:
        extractor = XFWBExtractor(logger)
        extracted_data = extractor.extract(root)
        print(f"Direct extraction result: {extracted_data}")
        
        awb_number = extracted_data.get('awb_number')
        if awb_number:
            print(f"✅ AWB found via direct extract: {awb_number}")
        else:
            print(f"❌ No AWB found via direct extract")
            
    except Exception as e:
        print(f"❌ Direct extract error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    file_path = "/var/www/aircargomis/storage/app/private/xml-imports/1750860570_176-77415704.xml"
    debug_xfwb_extraction(file_path)
