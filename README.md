# IATA XML Parsers

This package contains Python parsers for IATA XML standards:

-   XFFM (Flight Manifest)
-   XFWB (Waybill)
-   XFZB (House Waybill)

## Features

-   Fast and efficient XML parsing using lxml
-   **Automatic XML type detection** based on TypeCode elements (NEW)
-   Database integration with PostgreSQL
-   Comprehensive error handling and logging
-   Support for batch processing of multiple files

## Important Note on Script Usage

### Currently Used Scripts

-   `run_parser.py` - Unified interface for all parsers (NEW)
-   `process_xffm.py` - Main script for XFFM parsing (used by <PERSON><PERSON>)
-   `process_xfwb.py` - Main script for XFWB parsing (used by <PERSON><PERSON>)
-   `process_xfzb.py` - Main script for XFZB parsing (used by <PERSON><PERSON>)
-   `parsers/` - Modular parser architecture with extractors, validators, and database operations
-   `config/database.py` - Database configuration (use database.template.py as a starting point)
-   `utils/db_utils.py` - Database utilities (use db_utils.template.py as a starting point)

## Directory Structure

```
xml-parsers/
├── config/                  # Configuration files
│   ├── __init__.py
│   ├── database.py          # Database configuration
│   ├── database.template.py # Database configuration template
│   └── namespaces.py        # XML namespace definitions
├── parsers/                 # Modular parser architecture
│   ├── __init__.py
│   ├── xffm_parser.py       # XFFM parser with modular architecture
│   ├── xfwb_parser.py       # XFWB parser with modular architecture
│   ├── xfzb_parser.py       # XFZB parser with modular architecture
│   ├── extractors/          # Data extraction modules
│   │   ├── __init__.py
│   │   ├── xffm_extractor.py
│   │   ├── xfwb_extractor.py
│   │   └── xfzb_extractor.py
│   ├── validators/          # Data validation modules
│   │   ├── __init__.py
│   │   ├── xffm_validator.py
│   │   ├── xfwb_validator.py
│   │   └── xfzb_validator.py
│   └── database/            # Database operation modules
│       ├── __init__.py
│       ├── base_operations.py
│       ├── xffm_operations.py
│       ├── xfwb_operations.py
│       └── xfzb_operations.py
├── utils/                   # Utility functions
│   ├── __init__.py
│   ├── db_utils.py          # Database utilities
│   ├── db_utils.template.py # Database utilities template
│   ├── message_type_detector.py # Auto-detection logic
│   └── xml_utils.py         # XML utilities
├── run_parser.py            # Unified parser interface
├── process_xffm.py          # Main script for XFFM parsing (used by Laravel)
├── process_xfwb.py          # Main script for XFWB parsing (used by Laravel)
├── process_xfzb.py          # Main script for XFZB parsing (used by Laravel)
├── test_parsers.py          # Parser testing utility
├── requirements.txt         # Python dependencies
└── README.md                # Documentation
```

## Installation

1. Create a virtual environment:

```bash
python3 -m venv venv
source venv/bin/activate
```

2. Install the required packages:

```bash
pip install -r requirements.txt
```

## Usage

### Using the Unified Parser Interface (NEW)

The new `run_parser.py` script provides a unified interface to run all parsers:

```bash
# Process a single file (automatically detects parser type from XML content)
./run_parser.py -f path/to/file.xml

# Process a directory of files
./run_parser.py -d path/to/directory

# Specify parser type explicitly (bypasses auto-detection)
./run_parser.py -f path/to/file.xml -t xfwb

# Save results to a JSON file
./run_parser.py -f path/to/file.xml -o results.json

# Show verbose output
./run_parser.py -f path/to/file.xml -v

# Process with specific branch and user IDs
./run_parser.py -f path/to/file.xml -b 1 -u 1
```

## Auto-Detection Feature (NEW)

The parsers now support automatic message type detection based on XML content analysis. The system examines the `TypeCode` element to determine the appropriate parser:

### Detection Rules

-   **XFFM (Flight Manifest)**: `TypeCode = 122` with specific attributes
-   **XFWB (Air Waybill)**: `TypeCode = 741` (Master with houses) or `740` (Direct without houses)
-   **XFZB (House Air Waybill)**: `TypeCode = 703`

### Usage Examples

```bash
# Auto-detection (recommended)
python run_parser.py -f sample.xml

# Test auto-detection
python demo_auto_detection.py

# Run detection tests
python -m pytest test_message_type_detection.py -v
```

For detailed information about the auto-detection feature, see `AUTO_DETECTION_README.md`.

### Using the XFFM Parser

```bash
# Parse an XFFM file and save to database
python process_xffm.py path/to/file.xml

# Parse an XFFM file with specific branch and user IDs
python process_xffm.py path/to/file.xml 1 1
```

### Using the XFWB Parser

```bash
# Parse an XFWB file and save to database
python process_xfwb.py path/to/file.xml

# Parse an XFWB file with specific branch and user IDs
python process_xfwb.py path/to/file.xml 1 1
```

### Using the XFZB Parser

```bash
# Parse an XFZB file and save to database
python process_xfzb.py path/to/file.xml

# Parse an XFZB file with specific branch and user IDs
python process_xfzb.py path/to/file.xml 1 1
```

### Processing Multiple Files

```bash
# Process all XFFM files in a directory
python process_xffm.py -d path/to/directory

# Process all XFWB files in a directory
python process_xfwb.py -d path/to/directory

# Process all XFZB files in a directory
python process_xfzb.py -d path/to/directory
```

## Command Line Options

### Unified Parser Options (run_parser.py)

-   `-f, --file`: Path to an XML file
-   `-d, --directory`: Path to a directory containing XML files
-   `-t, --type`: Type of parser to use (xffm, xfwb, xfzb)
-   `-b, --branch-id`: Branch ID (default: 1)
-   `-u, --user-id`: User ID (default: 1)
-   `-o, --output`: Output file for results (JSON format)
-   `-v, --verbose`: Enable verbose output

### Individual Parser Options

-   `path`: Path to XML file
-   `-f, --file`: Path to an XML file
-   `-d, --directory`: Path to a directory containing XML files
-   `-b, --branch-id`: Branch ID (default: 1)
-   `-u, --user-id`: User ID (default: 1)

## Database Schema

The parsers expect the following database tables:

-   `flight_manifests`: For storing flight manifest data
-   `waybills`: For storing waybill data
-   `house_waybills`: For storing house waybill data
-   `consignments`: For storing consignment data
-   `goods`: For storing goods data
-   `charges`: For storing charges data
-   `ulds`: For storing ULD data
-   `flight_manifest_ulds`: For linking ULDs to flight manifests
-   `consignment_ulds`: For linking consignments to ULDs

## Configuration

### Database Configuration

1. Create your database configuration file:

```bash
cp config/database.template.py config/database.py
```

2. Edit `config/database.py` with your database credentials:

```python
DB_CONFIG = {
    'host': 'localhost',
    'database': 'cargo_handling_system',
    'user': 'your_username',
    'password': 'your_password',
    'port': 5432
}
```

### Database Utilities

1. Create your database utilities file:

```bash
cp utils/db_utils.template.py utils/db_utils.py
```

The database utilities file uses the configuration from `config/database.py` to connect to the database.

### XML Namespaces

XML namespaces are configured in `config/namespaces.py`.

## Examples

### Using the XFFM Parser

```bash
# Parse an XFFM (Flight Manifest) File
python process_xffm.py /home/<USER>/cargohandlingsystem/examples/XFFM/XFFM_SAMPLE1.xml

# Parse with specific branch and user IDs
python process_xffm.py /home/<USER>/cargohandlingsystem/examples/XFFM/XFFM_SAMPLE1.xml 1 1
```

### Using the XFWB Parser

```bash
# Parse an XFWB (Waybill) File
python process_xfwb.py /home/<USER>/cargohandlingsystem/examples/XFWB/XFWB_SAMPLE1.xml

# Parse with specific branch and user IDs
python process_xfwb.py /home/<USER>/cargohandlingsystem/examples/XFWB/XFWB_SAMPLE1.xml 1 1
```

### Using the XFZB Parser

```bash
# Parse an XFZB (House Waybill) File
python process_xfzb.py /home/<USER>/cargohandlingsystem/examples/XFZB/XFZB_SAMPLE1.xml

# Parse with specific branch and user IDs
python process_xfzb.py /home/<USER>/cargohandlingsystem/examples/XFZB/XFZB_SAMPLE1.xml 1 1
```

## Integration with Laravel

### Current Integration

The XML import functionality is integrated with Laravel through the `XmlImportController` which calls the individual Python scripts.

```php
// In XmlImportController.php
$scriptPath = base_path('python/xml-parsers/process_' . strtolower($request->xml_type) . '.py');
```

### Recommended Integration with Unified Parser

For better maintainability, consider updating the Laravel integration to use the unified parser interface:

```php
// In XmlImportController.php
$scriptPath = base_path('python/xml-parsers/run_parser.py');
$command = "python {$scriptPath} -f {$filePath} -t " . strtolower($request->xml_type) . " -b {$branchId} -u {$userId}";
```
