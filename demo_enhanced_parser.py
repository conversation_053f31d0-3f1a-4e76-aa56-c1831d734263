#!/usr/bin/env python3
"""
Demonstration script for the Enhanced XML Parser System.

This script demonstrates the key features of the enhanced parser including:
- Comprehensive validation
- Data quality assessment
- CIMP segment processing
- ONE Record conversion
- ULD management
- Performance monitoring
"""

import sys
import os
import json
from pathlib import Path

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from parsers.enhanced_base_parser import EnhancedBaseParser
from config.parser_config import ParserConfiguration


def demo_enhanced_parser():
    """Demonstrate the enhanced parser capabilities."""
    print("=" * 80)
    print("Enhanced XML Parser System Demonstration")
    print("Air Cargo Malawi - Enhanced Parser with Comprehensive Validation")
    print("=" * 80)
    
    # Sample XFWB XML data (simplified for demo)
    sample_xfwb_xml = """<?xml version="1.0" encoding="UTF-8"?>
<ns2:WaybillMessage xmlns:ns2="iata:datamodel:cargo:waybill:2">
    <ns2:MessageHeaderDocument>
        <TypeCode>741</TypeCode>
        <ID>XFWB</ID>
    </ns2:MessageHeaderDocument>
    <ns2:BusinessHeaderDocument>
        <ID>888-11111111</ID>
    </ns2:BusinessHeaderDocument>
    <ns2:MasterConsignment>
        <OriginLocation>
            <ID>ORK</ID>
        </OriginLocation>
        <FinalDestinationLocation>
            <ID>PVG</ID>
        </FinalDestinationLocation>
        <IncludedTareGrossWeightMeasure>1042.0</IncludedTareGrossWeightMeasure>
        <TotalPieceQuantity>3</TotalPieceQuantity>
        <ConsignorParty>
            <n>FORWARDER COMPANY IRELAND LTD</n>
            <PostalStructuredAddress>
                <StreetName>123 BUSINESS PARK</StreetName>
                <CityName>CORK</CityName>
                <CountryID>IE</CountryID>
                <PostcodeCode>T12 ABC1</PostcodeCode>
            </PostalStructuredAddress>
        </ConsignorParty>
        <ConsigneeParty>
            <n>FORWARDER COMPANY SHANGHAI LTD</n>
            <PostalStructuredAddress>
                <StreetName>456 INDUSTRIAL ZONE</StreetName>
                <CityName>SHANGHAI</CityName>
                <CountryID>CN</CountryID>
                <PostcodeCode>200000</PostcodeCode>
            </PostalStructuredAddress>
        </ConsigneeParty>
        <NatureIdentificationTransportCargo>
            <Identification>CONSOLIDATION AS PER ATTACHED MANIFEST</Identification>
        </NatureIdentificationTransportCargo>
    </ns2:MasterConsignment>
</ns2:WaybillMessage>"""
    
    try:
        print("\n1. Initializing Enhanced Parser")
        print("-" * 40)
        
        # Initialize parser with configuration
        config_file = "config/enhanced_parser_config.yaml"
        parser = EnhancedBaseParser(
            xml_string=sample_xfwb_xml,
            xml_type="xfwb",
            config_file=config_file,
            user_id=1,
            branch_id=1
        )
        
        print(f"✓ Parser initialized with configuration: {config_file}")
        print(f"✓ Strict mode: {parser.strict_mode}")
        print(f"✓ ULD tracking: {parser.config.is_uld_tracking_enabled()}")
        
        print("\n2. Processing XML with Enhanced Validation")
        print("-" * 40)
        
        # Parse with comprehensive validation
        results = parser.parse_with_validation()
        
        print("✓ XML parsing completed successfully")
        
        print("\n3. Validation Results")
        print("-" * 40)
        
        validation_report = results['validation_report']
        print(f"Validation Summary: {validation_report['summary']['total_count']} total messages")
        print(f"  - Errors: {validation_report['summary']['error_count']}")
        print(f"  - Warnings: {validation_report['summary']['warning_count']}")
        print(f"  - Hints: {validation_report['summary']['hint_count']}")
        print(f"  - Valid: {validation_report['summary']['is_valid']}")
        print(f"  - Duration: {validation_report['summary']['duration_ms']}ms")
        
        # Show validation messages if any
        if validation_report['errors']:
            print("\nValidation Errors:")
            for error in validation_report['errors']:
                print(f"  ❌ [{error['code']}] {error['message']}")
        
        if validation_report['warnings']:
            print("\nValidation Warnings:")
            for warning in validation_report['warnings'][:3]:  # Show first 3
                print(f"  ⚠️  [{warning['code']}] {warning['message']}")
        
        print("\n4. Data Quality Assessment")
        print("-" * 40)
        
        quality_metrics = results['quality_metrics']
        print(f"Overall Quality Score: {quality_metrics['overall_score']:.1%}")
        print(f"  - Completeness: {quality_metrics['completeness_score']:.1%}")
        print(f"  - Accuracy: {quality_metrics['accuracy_score']:.1%}")
        print(f"  - Consistency: {quality_metrics['consistency_score']:.1%}")
        
        # Show quality recommendations
        if quality_metrics['recommendations']:
            print("\nQuality Recommendations:")
            for rec in quality_metrics['recommendations'][:3]:  # Show first 3
                print(f"  💡 {rec}")
        
        print("\n5. ONE Record Conversion")
        print("-" * 40)
        
        if 'one_record_json' in results:
            one_record = results['one_record_json']
            print(f"✓ ONE Record conversion successful")
            print(f"  - Type: {one_record.get('@type', 'Unknown')}")
            print(f"  - Waybill Type: {one_record.get('waybillType', 'Unknown')}")
            print(f"  - AWB Number: {one_record.get('waybillPrefix', '')}-{one_record.get('waybillNumber', '')}")
            print(f"  - Ontology Version: {one_record.get('_metadata', {}).get('ontologyVersion', 'Unknown')}")
        else:
            print("❌ ONE Record conversion not available")
        
        print("\n6. ULD Management")
        print("-" * 40)
        
        if 'uld_data' in results and results['uld_data']:
            uld_data = results['uld_data']
            print(f"✓ ULD processing completed")
            print(f"  - ULDs processed: {uld_data.get('uld_count', 0)}")
        else:
            print("ℹ️  No ULD data found in this waybill")
        
        print("\n7. Performance Metrics")
        print("-" * 40)
        
        metrics = parser.get_performance_metrics()
        print(f"Files processed: {metrics['files_processed']}")
        print(f"Average processing time: {metrics['average_processing_time_ms']:.1f}ms")
        print(f"Memory usage: {metrics['memory_usage_mb']:.1f}MB")
        print(f"CPU usage: {metrics['cpu_usage_percent']:.1f}%")
        print(f"Throughput: {metrics['throughput_files_per_hour']:.1f} files/hour")
        
        print("\n8. System Health Status")
        print("-" * 40)
        
        health = parser.get_health_status()
        status_icon = "✅" if health['healthy'] else "❌"
        print(f"{status_icon} System Health: {'Healthy' if health['healthy'] else 'Unhealthy'}")
        print(f"  - Database: {'✅' if health.get('database', False) else '❌'}")
        print(f"  - Performance: {'✅' if health.get('performance', False) else '❌'}")
        print(f"  - Resources: {'✅' if health.get('resources', False) else '❌'}")
        print(f"  - Uptime: {health.get('uptime_seconds', 0):.1f}s")
        
        print("\n9. Sample Output Data")
        print("-" * 40)
        
        # Show sample of parsed data
        database_record = results['database_record']
        print("Parsed Waybill Data (sample):")
        sample_fields = ['awb_number', 'origin_airport', 'destination_airport', 
                        'total_weight', 'total_pieces', 'shipper_name']
        for field in sample_fields:
            if field in database_record:
                print(f"  {field}: {database_record[field]}")
        
        print("\n10. Export Capabilities")
        print("-" * 40)
        
        # Export metrics
        metrics_json = parser.export_metrics('json')
        print("✓ Performance metrics exported to JSON")
        
        # Save ONE Record to file (if available)
        if 'one_record_json' in results:
            one_record_file = "sample_one_record_output.json"
            with open(one_record_file, 'w') as f:
                json.dump(results['one_record_json'], f, indent=2)
            print(f"✓ ONE Record data saved to: {one_record_file}")
        
        print("\n" + "=" * 80)
        print("Enhanced Parser Demonstration Completed Successfully!")
        print("=" * 80)
        
        # Cleanup
        parser.cleanup()
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_configuration_features():
    """Demonstrate configuration management features."""
    print("\n" + "=" * 80)
    print("Configuration Management Demonstration")
    print("=" * 80)
    
    try:
        # Load configuration
        config = ParserConfiguration("config/enhanced_parser_config.yaml")
        
        print("\n1. Configuration Loading")
        print("-" * 40)
        print("✓ Configuration loaded from YAML file")
        
        # Show key configuration values
        print(f"Strict mode: {config.is_strict_mode()}")
        print(f"ULD tracking: {config.is_uld_tracking_enabled()}")
        print(f"Bulk cargo types: {config.get_bulk_cargo_types()}")
        
        print("\n2. Configuration Validation")
        print("-" * 40)
        issues = config.validate_config()
        if not issues:
            print("✅ Configuration is valid")
        else:
            print("❌ Configuration issues found:")
            for issue in issues:
                print(f"  - {issue}")
        
        print("\n3. Environment-specific Settings")
        print("-" * 40)
        print("Configuration supports environment-specific overrides:")
        print("  - development: Debug logging, relaxed validation")
        print("  - staging: Moderate thresholds, alerts enabled")
        print("  - production: Strict validation, optimized performance")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration demo error: {e}")
        return False


if __name__ == "__main__":
    print("Starting Enhanced XML Parser System Demonstration...")
    
    # Run main demonstration
    success = demo_enhanced_parser()
    
    if success:
        # Run configuration demonstration
        demo_configuration_features()
        
        print("\n🎉 All demonstrations completed successfully!")
        print("\nNext Steps:")
        print("1. Review the generated sample_one_record_output.json file")
        print("2. Check the xml_parser.log file for detailed logging")
        print("3. Explore the configuration options in enhanced_parser_config.yaml")
        print("4. Run the test suite: python test_enhanced_parser.py")
        print("5. Integrate with your existing XML processing workflows")
    else:
        print("\n❌ Demonstration failed. Please check the error messages above.")
        sys.exit(1)
