#!/usr/bin/env python3
"""
Demonstration script for XML message type auto-detection.
Shows how the detection works with different XML samples.
"""

import os
import sys

# Add the current directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.message_type_detector import MessageTypeDetector


def demo_detection_with_samples():
    """Demonstrate detection with sample XML strings."""
    print("XML Message Type Auto-Detection Demo")
    print("=" * 50)
    
    detector = MessageTypeDetector()
    
    # Sample XML strings for each message type
    samples = {
        "XFFM (Flight Manifest)": '''<?xml version="1.0" encoding="utf-8"?>
<ns0:FlightManifest xmlns:ns0="iata:flightmanifest:1" xmlns:ram="iata:datamodel:3">
    <ns0:MessageHeaderDocument>
        <ram:ID>DEMO_FLIGHT</ram:ID>
        <ram:Name>Transport Loading Report</ram:Name>
        <ram:TypeCode listID="1001" listAgencyID="6" listVersionID="D09A">122</ram:TypeCode>
        <ram:IssueDateTime>2025-05-24T12:00:00</ram:IssueDateTime>
    </ns0:MessageHeaderDocument>
</ns0:FlightManifest>''',
        
        "XFWB (Master Air Waybill - TypeCode 741)": '''<?xml version="1.0" encoding="UTF-8"?>
<rsm:Waybill xmlns:rsm="iata:waybill:1" xmlns:ram="iata:datamodel:3">
    <rsm:MessageHeaderDocument>
        <ram:ID>176-DEMO741</ram:ID>
        <ram:Name>Master Air Waybill</ram:Name>
        <ram:TypeCode>741</ram:TypeCode>
        <ram:IssueDateTime>2025-05-24T12:00:00</ram:IssueDateTime>
    </rsm:MessageHeaderDocument>
</rsm:Waybill>''',
        
        "XFWB (Master Air Waybill - TypeCode 740)": '''<?xml version="1.0" encoding="UTF-8"?>
<rsm:Waybill xmlns:rsm="iata:waybill:1" xmlns:ram="iata:datamodel:3">
    <rsm:MessageHeaderDocument>
        <ram:ID>176-DEMO740</ram:ID>
        <ram:Name>Air Waybill</ram:Name>
        <ram:TypeCode>740</ram:TypeCode>
        <ram:IssueDateTime>2025-05-24T12:00:00</ram:IssueDateTime>
    </rsm:MessageHeaderDocument>
</rsm:Waybill>''',
        
        "XFZB (House Air Waybill)": '''<?xml version="1.0" encoding="UTF-8"?>
<rsm:HouseWaybill xmlns:rsm="iata:housewaybill:1" xmlns:ram="iata:datamodel:3">
    <rsm:MessageHeaderDocument>
        <ram:ID>176-DEMO-H01</ram:ID>
        <ram:Name>House waybill</ram:Name>
        <ram:TypeCode>703</ram:TypeCode>
        <ram:IssueDateTime>2025-05-24T12:00:00</ram:IssueDateTime>
    </rsm:MessageHeaderDocument>
</rsm:HouseWaybill>''',
        
        "Different Namespace Prefix": '''<?xml version="1.0" encoding="UTF-8"?>
<rsm:Waybill xmlns:rsm="iata:waybill:1" xmlns:ns0="iata:datamodel:3">
    <rsm:MessageHeaderDocument>
        <ns0:ID>176-DEMO-NS0</ns0:ID>
        <ns0:Name>Air Waybill</ns0:Name>
        <ns0:TypeCode>740</ns0:TypeCode>
        <ns0:IssueDateTime>2025-05-24T12:00:00</ns0:IssueDateTime>
    </rsm:MessageHeaderDocument>
</rsm:Waybill>''',
        
        "Invalid XFFM (TypeCode 122 without attributes)": '''<?xml version="1.0" encoding="utf-8"?>
<ns0:FlightManifest xmlns:ns0="iata:flightmanifest:1" xmlns:ram="iata:datamodel:3">
    <ns0:MessageHeaderDocument>
        <ram:ID>INVALID_FLIGHT</ram:ID>
        <ram:Name>Invalid Document</ram:Name>
        <ram:TypeCode>122</ram:TypeCode>
        <ram:IssueDateTime>2025-05-24T12:00:00</ram:IssueDateTime>
    </ns0:MessageHeaderDocument>
</ns0:FlightManifest>''',
        
        "Unknown TypeCode": '''<?xml version="1.0" encoding="UTF-8"?>
<root xmlns:ram="iata:datamodel:3">
    <ram:TypeCode>999</ram:TypeCode>
</root>''',
        
        "No TypeCode": '''<?xml version="1.0" encoding="UTF-8"?>
<root xmlns:ram="iata:datamodel:3">
    <ram:ID>NO_TYPE_CODE</ram:ID>
    <ram:Name>Document without TypeCode</ram:Name>
</root>'''
    }
    
    for description, xml_content in samples.items():
        print(f"\n{description}:")
        print("-" * len(description))
        
        detected_type = detector.detect_from_string(xml_content)
        
        if detected_type:
            print(f"✓ Detected: {detected_type.upper()}")
        else:
            print("✗ No message type detected")
        
        # Show the relevant TypeCode line
        lines = xml_content.split('\n')
        for line in lines:
            if 'TypeCode' in line:
                print(f"  TypeCode line: {line.strip()}")
                break
        else:
            print("  No TypeCode element found")


def demo_detection_rules():
    """Display the detection rules."""
    print("\n\nDetection Rules")
    print("=" * 50)
    
    detector = MessageTypeDetector()
    rules = detector.get_detection_rules()
    
    for msg_type, rule in rules.items():
        print(f"\n{msg_type.upper()} ({rule['description']}):")
        print(f"  TypeCode: {rule['type_code']}")
        
        if rule['required_attributes']:
            print("  Required attributes:")
            for attr, value in rule['required_attributes'].items():
                print(f"    {attr}=\"{value}\"")
        else:
            print("  No additional attributes required")


def demo_with_sample_files():
    """Demonstrate detection with actual sample files."""
    print("\n\nSample File Detection")
    print("=" * 50)
    
    detector = MessageTypeDetector()
    
    # Sample files to test (if they exist)
    sample_files = [
        ("../xmlmakerapp/samples/xffm/4Z460_19APR.xml", "XFFM"),
        ("../xmlmakerapp/samples/xfwb/176-09650384-XFWB.xml", "XFWB"),
        ("../xmlmakerapp/samples/xfzb/176-09650384-H03.xml", "XFZB"),
    ]
    
    for file_path, expected_type in sample_files:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            detected_type = detector.detect_from_file(file_path)
            
            if detected_type:
                status = "✓" if detected_type.upper() == expected_type else "✗"
                print(f"{status} {filename}: {detected_type.upper()} (expected {expected_type})")
            else:
                print(f"✗ {filename}: No type detected (expected {expected_type})")
        else:
            print(f"- {file_path}: File not found")


def demo_api_usage():
    """Show different ways to use the detection API."""
    print("\n\nAPI Usage Examples")
    print("=" * 50)
    
    print("\n1. Using MessageTypeDetector directly:")
    print("   from utils.message_type_detector import MessageTypeDetector")
    print("   detector = MessageTypeDetector()")
    print("   message_type = detector.detect_from_file('sample.xml')")
    
    print("\n2. Using BaseParser static methods:")
    print("   from parsers.base_parser import BaseParser")
    print("   message_type = BaseParser.detect_message_type_from_file('sample.xml')")
    
    print("\n3. Auto-detection during BaseParser initialization:")
    print("   parser = BaseParser(xml_file='sample.xml')  # xml_type auto-detected")
    print("   print(f'Detected type: {parser.xml_type}')")
    
    print("\n4. Command line usage:")
    print("   python run_parser.py -f sample.xml  # Auto-detects type")
    print("   python run_parser.py -f sample.xml -t xfwb  # Explicit type")


if __name__ == '__main__':
    # Run all demonstrations
    demo_detection_with_samples()
    demo_detection_rules()
    demo_with_sample_files()
    demo_api_usage()
    
    print("\n" + "=" * 50)
    print("Demo completed! 🎉")
    print("\nFor more information, see AUTO_DETECTION_README.md")
