#!/usr/bin/env python3
"""
Simple XFFM operations based on the old working parser.
This module contains simplified database operations for XFFM processing
without complex enhancements that can cause issues.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime


class SimpleXFFMOperations:
    """Simple XFFM database operations based on the old working parser."""

    def __init__(self, db_connection, db_cursor, branch_id: int, user_id: int, logger=None):
        self.db_connection = db_connection
        self.db_cursor = db_cursor
        self.branch_id = branch_id
        self.user_id = user_id
        self.logger = logger or logging.getLogger(__name__)

        # Initialize unified suffix service for consistent suffix generation
        from services.unified_suffix_service import UnifiedSuffixService
        self.suffix_service = UnifiedSuffixService(
            db_connection, db_cursor, branch_id, logger
        )

    def save_master_waybill(self, waybill: Dict[str, Any], manifest_id: str) -> Optional[int]:
        """
        Save master waybill to database (based on old parser).

        Args:
            waybill (dict): Waybill data
            manifest_id (str): Manifest ID

        Returns:
            int: AWB ID if created/found, None otherwise
        """
        try:
            awb_number = waybill.get('awb_number')
            if not awb_number:
                self.logger.error("No AWB number provided")
                return None

            # Check if waybill already exists
            check_query = """
                SELECT awb_id FROM master_waybills
                WHERE awb_number = %s
            """
            try:
                self.db_cursor.execute(check_query, (awb_number,))
                result = self.db_cursor.fetchone()

                if result:
                    awb_id = result[0]
                    self.logger.info(f"Master waybill already exists: {awb_number} (ID: {awb_id})")
                    return awb_id
            except Exception as check_error:
                self.logger.error(f"Error checking existing waybill for {awb_number}: {str(check_error)}")
                # Rollback and retry
                self.db_connection.rollback()
                return None

            # Get flight data for fallbacks
            flight_data = self.get_flight_data(manifest_id)

            # Ensure origin_airport is never null
            origin_airport = waybill.get('origin_airport')
            if not origin_airport:
                origin_airport = flight_data.get('departure_airport') if flight_data else 'LLW'
                self.logger.warning(f"Using fallback origin airport {origin_airport} for AWB {awb_number}")

            # Ensure destination_airport is never null
            destination_airport = waybill.get('destination_airport')
            if not destination_airport:
                destination_airport = flight_data.get('arrival_airport') if flight_data else 'JNB'
                self.logger.warning(f"Using fallback destination airport {destination_airport} for AWB {awb_number}")

            # Insert new master waybill
            insert_query = """
                INSERT INTO master_waybills (
                    awb_number, type_code, manifest_id, origin_airport,
                    destination_airport, total_pieces, total_weight, weight_unit,
                    gross_volume, volume_unit, special_handling_code, summary_description,
                    status, is_placeholder, in_transit, total_pieces_expected, received_pieces_count,
                    branch_id, created_by, updated_by, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, NOW(), NOW()
                )
                RETURNING awb_id
            """

            # Prepare values
            gross_volume = waybill.get('volume')
            volume_unit = waybill.get('volume_unit') if gross_volume else None

            values = (
                awb_number,
                waybill.get('type_code', '740'),
                manifest_id,
                origin_airport,
                destination_airport,
                waybill.get('pieces', 0),
                waybill.get('weight', 0),
                waybill.get('weight_unit', 'KGM'),
                gross_volume,
                volume_unit,
                waybill.get('special_handling_code'),
                waybill.get('description'),
                'PENDING',  # status
                False,  # is_placeholder
                False,  # in_transit
                waybill.get('pieces', 0),  # total_pieces_expected
                0,  # received_pieces_count
                self.branch_id,
                self.user_id,
                self.user_id
            )

            try:
                self.db_cursor.execute(insert_query, values)
                awb_id = self.db_cursor.fetchone()[0]

                self.logger.info(f"✅ Created master waybill: {awb_number} (ID: {awb_id})")
                return awb_id
            except Exception as insert_error:
                self.logger.error(f"❌ Error inserting master waybill {awb_number}: {str(insert_error)}")
                # Rollback transaction on error
                try:
                    self.db_connection.rollback()
                except:
                    pass
                return None

        except Exception as e:
            self.logger.error(f"❌ Error saving master waybill {waybill.get('awb_number', 'UNKNOWN')}: {str(e)}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            # Rollback transaction on error
            try:
                self.db_connection.rollback()
            except:
                pass
            return None

    def create_uld_awb_allocation(self, awb_number: str, uld_id: str, manifest_id: str,
                                 awb_data: Dict[str, Any]) -> Optional[int]:
        """
        Create ULD-AWB allocation (based on old parser).

        Args:
            awb_number (str): AWB number
            uld_id (str): ULD ID
            manifest_id (str): Manifest ID
            awb_data (dict): AWB data

        Returns:
            int: Allocation ID if created, None otherwise
        """
        try:
            # Check if allocation already exists
            check_query = """
                SELECT id FROM uld_awbs
                WHERE uld_id = %s AND manifest_id = %s AND awb_number = %s
            """
            try:
                self.db_cursor.execute(check_query, (uld_id, manifest_id, awb_number))
                result = self.db_cursor.fetchone()

                if result:
                    allocation_id = result[0]
                    self.logger.info(f"ULD allocation already exists: {awb_number} in {uld_id} (ID: {allocation_id})")
                    return allocation_id
            except Exception as check_error:
                self.logger.error(f"Error checking existing ULD allocation: {str(check_error)}")
                try:
                    self.db_connection.rollback()
                except:
                    pass
                return None

            # Insert new allocation
            insert_query = """
                INSERT INTO uld_awbs (
                    uld_id, manifest_id, awb_number, pieces, weight,
                    volume, volume_unit, split_code, description, summary_description,
                    branch_id, created_by, updated_by, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW()
                )
                RETURNING id
            """

            values = (
                uld_id,
                manifest_id,
                awb_number,
                awb_data.get('pieces', 0),
                awb_data.get('weight', 0),
                awb_data.get('volume'),
                awb_data.get('volume_unit'),
                awb_data.get('split_type', 'T'),
                awb_data.get('description', ''),
                awb_data.get('description', ''),
                self.branch_id,
                self.user_id,
                self.user_id
            )

            try:
                self.db_cursor.execute(insert_query, values)
                allocation_id = self.db_cursor.fetchone()[0]

                self.logger.info(f"✅ Created ULD allocation: {awb_number} in {uld_id} (ID: {allocation_id})")
                return allocation_id
            except Exception as insert_error:
                self.logger.error(f"❌ Error inserting ULD allocation for {awb_number} in {uld_id}: {str(insert_error)}")
                try:
                    self.db_connection.rollback()
                except:
                    pass
                return None

        except Exception as e:
            self.logger.error(f"❌ Error creating ULD allocation for {awb_number} in {uld_id}: {str(e)}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            try:
                self.db_connection.rollback()
            except:
                pass
            return None

    def create_partial_waybill(self, awb_number: str, manifest_id: str, uld_id: str,
                              awb_data: Dict[str, Any]) -> Optional[str]:
        """
        Create partial waybill (based on old parser).

        Args:
            awb_number (str): AWB number
            manifest_id (str): Manifest ID
            uld_id (str): ULD ID
            awb_data (dict): AWB data

        Returns:
            str: Partial ID if created, None otherwise
        """
        try:
            # Check if partial already exists for this AWB on this manifest
            check_query = """
                SELECT partial_id FROM partial_waybills
                WHERE master_awb_number = %s AND manifest_id = %s
            """
            self.db_cursor.execute(check_query, (awb_number, manifest_id))
            result = self.db_cursor.fetchone()

            if result:
                partial_id = result[0]
                self.logger.info(f"Partial already exists: {partial_id}")
                return partial_id

            # Use unified suffix service for consistent suffix generation
            flight_number = awb_data.get('flight_number', 'UNKNOWN')
            flight_date = awb_data.get('flight_date', '2024-01-01')
            split_type = awb_data.get('split_type', 'P')

            # Get sequence number for partial waybill
            count_query = """
                SELECT COUNT(*) FROM partial_waybills
                WHERE master_awb_number = %s
            """
            self.db_cursor.execute(count_query, (awb_number,))
            sequence = self.db_cursor.fetchone()[0] + 1

            # Generate suffix using unified service (matches Laravel implementation)
            if self.suffix_service.requires_suffix(split_type):
                suffix = self.suffix_service.generate_suffix(
                    awb_number, manifest_id, flight_number, flight_date, split_type
                )
                partial_id = f"PWB-{suffix}"
            else:
                # Fallback for non-partial types
                partial_id = f"PWB-{awb_number}-{sequence}"

            # Insert partial waybill
            insert_query = """
                INSERT INTO partial_waybills (
                    partial_id, master_awb_number, manifest_id,
                    origin_airport, destination_airport,
                    expected_pieces, expected_weight, received_pieces, received_weight,
                    remaining_pieces, remaining_weight, split_code, split_sequence,
                    status, special_handling_code, summary_description, uld_id,
                    weight_unit, gross_volume, volume_unit, source,
                    branch_id, created_by, updated_by, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    %s, %s, %s, %s, %s, %s, %s, NOW(), NOW()
                )
                RETURNING id
            """

            values = (
                partial_id,
                awb_number,
                manifest_id,
                awb_data.get('origin_airport', ''),
                awb_data.get('destination_airport', ''),
                awb_data.get('pieces', 0),  # expected_pieces
                awb_data.get('weight', 0),  # expected_weight
                awb_data.get('pieces', 0),  # received_pieces (for XFFM, assume received = expected)
                awb_data.get('weight', 0),  # received_weight
                0,  # remaining_pieces
                0,  # remaining_weight
                awb_data.get('split_type', 'P'),  # split_code
                sequence,  # split_sequence
                'CHECKED_IN',  # status (for XFFM, mark as checked in)
                awb_data.get('special_handling_code'),
                awb_data.get('description'),
                uld_id,
                awb_data.get('weight_unit', 'KGM'),
                awb_data.get('volume'),
                awb_data.get('volume_unit'),
                'XFFM',  # source
                self.branch_id,
                self.user_id,
                self.user_id
            )

            self.db_cursor.execute(insert_query, values)

            self.logger.info(f"✅ Created partial waybill: {partial_id}")
            return partial_id

        except Exception as e:
            self.logger.error(f"❌ Error creating partial waybill for {awb_number}: {str(e)}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            return None

    def get_flight_data(self, manifest_id: str) -> Optional[Dict[str, str]]:
        """
        Get flight data from database.

        Args:
            manifest_id (str): Manifest ID

        Returns:
            dict: Flight data or None
        """
        try:
            query = """
                SELECT departure_airport, arrival_airport
                FROM flight_manifests
                WHERE manifest_id = %s
            """
            self.db_cursor.execute(query, (manifest_id,))
            result = self.db_cursor.fetchone()

            if result:
                return {
                    'departure_airport': result[0],
                    'arrival_airport': result[1]
                }
            return None

        except Exception as e:
            self.logger.error(f"Error getting flight data: {str(e)}")
            return None
