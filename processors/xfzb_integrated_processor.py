#!/usr/bin/env python3
"""
XFZB Integrated Processor for the Integrated Cargo XML Parser System.

This processor handles XFZB (House Waybill XML) files within the integrated system:
- Parses XFZB XML files
- Links house waybills to existing master waybills
- <PERSON>les house waybill data with proper validation
- Integrates with the two-stage processing workflow
- Logs processing activity for audit trails
"""

import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

from database.integrated_operations import IntegratedDatabaseOperations
from extractors.xfzb_extractor import XFZBExtractor


class XFZBIntegratedProcessor:
    """
    XFZB integrated processor for house waybill processing.

    Handles XFZB files within the integrated cargo processing system:
    1. Parse XFZB XML files
    2. Validate master AWB exists
    3. Create house waybill records
    4. Link to master waybills
    5. Log processing activity
    """

    def __init__(self, db_connection, db_cursor, branch_id=1, user_id=1, logger=None):
        """
        Initialize XFZB integrated processor.

        Args:
            db_connection: Database connection object
            db_cursor: Database cursor object
            branch_id (int): Current branch ID
            user_id (int): Current user ID
            logger: Logger instance
        """
        self.db_ops = IntegratedDatabaseOperations(
            db_connection, db_cursor, branch_id, user_id, logger
        )
        self.extractor = XFZBExtractor(logger)
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.branch_id = branch_id
        self.user_id = user_id

    def process_xfzb_file(self, file_path: str) -> Dict[str, Any]:
        """
        Process a single XFZB XML file.

        Args:
            file_path (str): Path to XFZB XML file

        Returns:
            dict: Processing result with statistics and status
        """
        start_time = time.time()
        result = {
            'success': False,
            'file_name': file_path.split('/')[-1],
            'message_id': None,
            'awb_count': 0,
            'house_awb_count': 0,
            'processed_hawbs': [],
            'errors': [],
            'warnings': [],
            'processing_time_ms': 0
        }

        try:
            self.logger.info(f"Starting XFZB processing for file: {file_path}")

            # Extract data from XFZB file
            extracted_data = self.extractor.extract_from_file(file_path)

            if not extracted_data or 'success' not in extracted_data or not extracted_data['success']:
                error_msg = f"Failed to extract data from XFZB file: {file_path}"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)
                return result

            # Get message ID for logging
            result['message_id'] = extracted_data.get('message_id', 'UNKNOWN')

            # Process house waybills
            house_waybills = extracted_data.get('house_waybills', [])
            if not house_waybills:
                error_msg = "No house waybill data found in XFZB file"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)
                return result

            # Process each house waybill
            for hawb_data in house_waybills:
                hawb_result = self.process_single_house_waybill(hawb_data, extracted_data)

                if hawb_result['success']:
                    result['processed_hawbs'].append(hawb_result['hawb_number'])
                    result['house_awb_count'] += 1
                    result['warnings'].extend(hawb_result.get('warnings', []))
                else:
                    result['errors'].extend(hawb_result.get('errors', []))

            # Set AWB count (for logging purposes)
            result['awb_count'] = result['house_awb_count']

            # Calculate processing time
            processing_time_ms = int((time.time() - start_time) * 1000)
            result['processing_time_ms'] = processing_time_ms

            # Log processing activity
            self.log_processing_activity(result)

            # Commit transaction if successful
            if result['house_awb_count'] > 0:
                self.db_ops.commit_transaction()
                result['success'] = True
                self.logger.info(f"Successfully processed XFZB file: {result['file_name']}")
            else:
                self.db_ops.rollback_transaction()
                self.logger.warning(f"No house AWBs processed from file: {result['file_name']}")

        except Exception as e:
            # Rollback transaction on error
            self.db_ops.rollback_transaction()
            error_msg = f"Error processing XFZB file {file_path}: {str(e)}"
            result['errors'].append(error_msg)
            result['processing_time_ms'] = int((time.time() - start_time) * 1000)
            self.logger.error(error_msg, exc_info=True)

            # Still log the failed processing attempt
            self.log_processing_activity(result)

        return result

    def process_single_house_waybill(self, hawb_data: Dict[str, Any],
                                    full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single house waybill from XFZB data.

        Args:
            hawb_data (dict): House waybill data extracted from XFZB
            full_extracted_data (dict): Complete extracted data for context

        Returns:
            dict: Processing result for this house waybill
        """
        result = {
            'success': False,
            'hawb_number': hawb_data.get('hawb_number', 'UNKNOWN'),
            'mawb_number': hawb_data.get('mawb_number', 'UNKNOWN'),
            'action': None,
            'errors': [],
            'warnings': []
        }

        try:
            hawb_number = hawb_data['hawb_number']
            mawb_number = hawb_data['mawb_number']

            self.logger.info(f"Processing House AWB: {hawb_number} for Master AWB: {mawb_number}")

            # Step 1: Validate master AWB exists
            master_awb = self.db_ops.find_record(
                'master_waybills',
                'awb_number = %s',
                (mawb_number,),
                'awb_id, awb_number, manifest_id, status'
            )

            if not master_awb:
                # Create orphan master AWB if it doesn't exist
                orphan_awb_data = self.prepare_orphan_master_awb_data(hawb_data, full_extracted_data)
                mawb_number, was_created = self.db_ops.create_or_update_master_waybill(
                    orphan_awb_data, is_orphan=True
                )
                result['warnings'].append(f"Created orphan master AWB {mawb_number} for house AWB {hawb_number}")

                # Re-fetch the master AWB
                master_awb = self.db_ops.find_record(
                    'master_waybills',
                    'awb_number = %s',
                    (mawb_number,),
                    'awb_id, awb_number, manifest_id, status'
                )

            # Step 2: Check if house AWB already exists
            existing_hawb = self.db_ops.find_record(
                'house_waybills',
                'hawb_number = %s',
                (hawb_number,),
                'hawb_id, hawb_number, status'
            )

            if existing_hawb:
                result['action'] = 'SKIPPED_EXISTS'
                result['warnings'].append(f"House AWB {hawb_number} already exists")
                self.logger.info(f"Skipping existing house AWB: {hawb_number}")
                result['success'] = True
                return result

            # Step 3: Create house waybill record
            hawb_id = self.create_house_waybill(hawb_data, master_awb, full_extracted_data)

            if hawb_id:
                result['action'] = 'CREATED'
                result['success'] = True
                self.logger.info(f"Created house waybill: {hawb_number} (ID: {hawb_id})")
            else:
                result['errors'].append(f"Failed to create house waybill {hawb_number}")

        except Exception as e:
            error_msg = f"Error processing house AWB {result['hawb_number']}: {str(e)}"
            result['errors'].append(error_msg)
            self.logger.error(error_msg, exc_info=True)

        return result

    def prepare_orphan_master_awb_data(self, hawb_data: Dict[str, Any],
                                      full_extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare orphan master AWB data from house waybill information.

        Args:
            hawb_data (dict): House waybill data
            full_extracted_data (dict): Complete extracted data

        Returns:
            dict: Prepared master AWB data for orphan creation
        """
        # Get current branch code for in-transit logic
        current_branch_code = self.db_ops.get_branch_code(self.branch_id)
        destination_code = hawb_data.get('destination_airport', '')

        # Determine if this is an in-transit shipment
        is_in_transit = False
        if destination_code != current_branch_code:
            # Check if destination has a branch
            dest_branch_id = self.db_ops.check_destination_branch_exists(destination_code)
            is_in_transit = dest_branch_id is not None

        return {
            'awb_number': hawb_data['mawb_number'],
            'type_code': '740',  # Default for orphan AWBs
            'manifest_id': full_extracted_data.get('manifest_id'),
            'origin_airport': hawb_data.get('origin_airport', ''),
            'destination_airport': hawb_data.get('destination_airport', ''),
            'total_pieces': hawb_data.get('total_pieces', 0),
            'total_weight': hawb_data.get('total_weight', 0),
            'weight_unit': hawb_data.get('weight_unit', 'KGM'),
            'gross_volume': hawb_data.get('gross_volume'),
            'volume_unit': hawb_data.get('volume_unit'),
            'summary_description': f"Consolidated shipment (from HAWB {hawb_data.get('hawb_number', '')})",
            'special_handling_code': hawb_data.get('special_handling_code'),
            'special_handling_codes': hawb_data.get('special_handling_codes', []),
            'goods_descriptions': [],
            'in_transit': is_in_transit,
            'xml_data': full_extracted_data
        }

    def create_house_waybill(self, hawb_data: Dict[str, Any], master_awb: Tuple,
                            full_extracted_data: Dict[str, Any]) -> Optional[int]:
        """
        Create house waybill record.

        Args:
            hawb_data (dict): House waybill data
            master_awb (tuple): Master AWB record (awb_id, awb_number, manifest_id, status)
            full_extracted_data (dict): Complete extracted data

        Returns:
            int: House waybill ID if successful, None otherwise
        """
        try:
            # Prepare house waybill data
            house_waybill_data = {
                'hawb_number': hawb_data['hawb_number'],
                'mawb_number': hawb_data['mawb_number'],
                'manifest_id': master_awb[2],  # Get manifest_id from master AWB
                'origin_airport': hawb_data.get('origin_airport', ''),
                'destination_airport': hawb_data.get('destination_airport', ''),
                'total_pieces': hawb_data.get('total_pieces', 0),
                'total_weight': hawb_data.get('total_weight', 0),
                'weight_unit': hawb_data.get('weight_unit', 'KGM'),
                'gross_volume': hawb_data.get('gross_volume'),
                'volume_unit': hawb_data.get('volume_unit'),
                'special_handling_code': hawb_data.get('special_handling_code'),
                'summary_description': hawb_data.get('description'),
                'is_mail': hawb_data.get('is_mail', False),
                'is_human_remains': hawb_data.get('is_human_remains', False),
                'is_partial': hawb_data.get('is_partial', False),
                'is_reconciled': False,
                'status': 'PENDING',
                'branch_id': self.branch_id,
            }

            # Store complete original XML content for audit trails
            xml_content = full_extracted_data.get('xml_content')
            if xml_content:
                import json
                house_waybill_data['xml_data'] = json.dumps({'xml': xml_content})

            # Add timestamps and user info
            self.db_ops.add_timestamps(house_waybill_data, self.user_id, self.user_id)

            # Insert house waybill
            hawb_id = self.db_ops.insert_record('house_waybills', house_waybill_data, 'hawb_id')

            # Save party information if available
            self.save_party_information(hawb_id, hawb_data)

            # Save special handling codes if available
            self.save_special_handling_codes(hawb_id, hawb_data)

            return hawb_id

        except Exception as e:
            self.logger.error(f"Error creating house waybill {hawb_data.get('hawb_number', 'UNKNOWN')}: {str(e)}")
            return None

    def save_party_information(self, hawb_id: int, hawb_data: Dict[str, Any]):
        """
        Save party information for house waybill.

        Args:
            hawb_id (int): House waybill ID
            hawb_data (dict): House waybill data
        """
        try:
            party_ids = {}

            # Save shipper
            shipper_data = hawb_data.get('shipper_data')
            if shipper_data:
                party_ids['shipper_code'] = self.find_or_create_shipper(shipper_data)

            # Save consignee
            consignee_data = hawb_data.get('consignee_data')
            if consignee_data:
                party_ids['consignee_code'] = self.find_or_create_consignee(consignee_data)

            # Update house waybill with party IDs
            if party_ids:
                self.db_ops.update_record('house_waybills', party_ids, 'hawb_id = %s', (hawb_id,))
                self.logger.info(f"Updated house AWB {hawb_id} with party information: {party_ids}")

        except Exception as e:
            self.logger.error(f"Error saving party information for house AWB {hawb_id}: {str(e)}")

    def find_or_create_shipper(self, shipper_data: Dict[str, Any]) -> Optional[int]:
        """
        Find or create shipper record.

        Args:
            shipper_data (dict): Shipper data

        Returns:
            int: Shipper ID if successful, None otherwise
        """
        try:
            name = shipper_data.get('name', '').strip()
            if not name:
                return None

            # Try to find existing shipper by name
            existing = self.db_ops.find_record(
                'shippers', 'LOWER(name) = LOWER(%s)', (name,), 'id'
            )

            if existing:
                return existing[0]

            # Create new shipper
            shipper_record = {
                'name': name,
                'address_line1': shipper_data.get('address', ''),
                'city': shipper_data.get('city', ''),
                'country_code': shipper_data.get('country_code', ''),
                'postal_code': shipper_data.get('postal_code', ''),
                'is_active': True,
            }

            # Add timestamps
            self.db_ops.add_timestamps(shipper_record)

            shipper_id = self.db_ops.insert_record('shippers', shipper_record, 'id')
            self.logger.info(f"Created new shipper: {name} (ID: {shipper_id})")
            return shipper_id

        except Exception as e:
            self.logger.error(f"Error finding/creating shipper: {str(e)}")
            return None

    def find_or_create_consignee(self, consignee_data: Dict[str, Any]) -> Optional[int]:
        """
        Find or create consignee record.

        Args:
            consignee_data (dict): Consignee data

        Returns:
            int: Consignee ID if successful, None otherwise
        """
        try:
            name = consignee_data.get('name', '').strip()
            if not name:
                return None

            # Try to find existing consignee by name
            existing = self.db_ops.find_record(
                'consignees', 'LOWER(name) = LOWER(%s)', (name,), 'id'
            )

            if existing:
                return existing[0]

            # Create new consignee
            consignee_record = {
                'name': name,
                'address_line1': consignee_data.get('address', ''),
                'city': consignee_data.get('city', ''),
                'country_code': consignee_data.get('country_code', ''),
                'postal_code': consignee_data.get('postal_code', ''),
                'is_active': True,
            }

            # Add timestamps
            self.db_ops.add_timestamps(consignee_record)

            consignee_id = self.db_ops.insert_record('consignees', consignee_record, 'id')
            self.logger.info(f"Created new consignee: {name} (ID: {consignee_id})")
            return consignee_id

        except Exception as e:
            self.logger.error(f"Error finding/creating consignee: {str(e)}")
            return None

    def save_special_handling_codes(self, hawb_id: int, hawb_data: Dict[str, Any]):
        """
        Save special handling codes for house waybill.

        Args:
            hawb_id (int): House waybill ID
            hawb_data (dict): House waybill data
        """
        try:
            # Save handling codes if any special handling codes exist
            shc_codes = hawb_data.get('special_handling_codes', [])
            for code in shc_codes:
                self.save_handling_instruction(hawb_id, code)

        except Exception as e:
            self.logger.error(f"Error saving special handling codes for house AWB {hawb_id}: {str(e)}")

    def save_handling_instruction(self, hawb_id: int, handling_code: str):
        """
        Save special handling code for house waybill.

        Args:
            hawb_id (int): House waybill ID
            handling_code (str): Special handling code
        """
        try:
            # Check if the special handling code exists in the master table
            shc_exists = self.db_ops.find_record(
                'special_handling_codes',
                'code = %s AND is_active = TRUE',
                (handling_code,),
                'id'
            )

            if not shc_exists:
                self.logger.warning(f"Special handling code '{handling_code}' not found in master table")
                return None

            # Check if this house AWB already has this SHC
            existing = self.db_ops.find_record(
                'awb_special_handling_codes',
                'hawb_id = %s AND code = %s AND is_house = TRUE',
                (hawb_id, handling_code),
                'id'
            )

            if existing:
                self.logger.info(f"Special handling code {handling_code} already exists for house AWB {hawb_id}")
                return existing[0]

            # Create new special handling code record
            instruction_data = {
                'hawb_id': hawb_id,
                'code': handling_code,
                'is_house': True,
            }

            # Add timestamps
            self.db_ops.add_timestamps(instruction_data)

            instruction_id = self.db_ops.insert_record('awb_special_handling_codes', instruction_data, 'id')
            self.logger.info(f"Saved special handling code {handling_code} with ID {instruction_id}")
            return instruction_id

        except Exception as e:
            self.logger.error(f"Error saving handling instruction {handling_code}: {str(e)}")
            return None

    def log_processing_activity(self, result: Dict[str, Any]):
        """
        Log processing activity to the processing_log table.

        Args:
            result (dict): Processing result
        """
        try:
            self.db_ops.log_processing(
                file_name=result['file_name'],
                message_id=result['message_id'],
                msg_type='XFZB',
                awb_count=result['awb_count'],
                partial_count=0,  # XFZB doesn't create partials
                processing_time_ms=result['processing_time_ms'],
                success=result['success'],
                error_message='; '.join(result['errors']) if result['errors'] else None
            )
        except Exception as e:
            self.logger.error(f"Failed to log processing activity: {str(e)}")
