#!/usr/bin/env python3
"""
Debug script to test XFFM database operations.
"""

import sys
import os
sys.path.append(os.getcwd())

import psycopg2
from config.database import DB_CONFIG
from database.fallback_operations import FallbackEnhancedOperations
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_operations():
    """Test basic database operations for XFFM processing."""
    try:
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Initialize database operations
        db_ops = FallbackEnhancedOperations(conn, cursor, branch_id=1, user_id=2, logger=logger)

        print("=== TESTING DATABASE OPERATIONS ===")

        # Test 0: Create flight manifest first
        print("\n0. Creating flight manifest first...")
        try:
            manifest_data = {
                'manifest_id': 'FM-TEST-20250617',
                'flight_number': 'TEST123',
                'flight_date': '2025-06-17',
                'carrier_code': 'TE',
                'departure_airport': 'NBO',
                'arrival_airport': 'LLW',
                'scheduled_departure': 'NOW()',
                'scheduled_arrival': 'NOW()',
                'flight_type': 'NORMAL',
                'status': 'SCHEDULED',
                'total_pieces': 0,
                'total_weight': 0,
                'branch_id': 1,
                'created_by': 2,
                'updated_by': 2,
                'xml_data': None
            }

            db_ops.add_timestamps(manifest_data, 2, 2)
            db_ops.insert_record('flight_manifests', manifest_data)
            conn.commit()  # Commit the manifest immediately
            print("✅ Flight manifest created and committed")
        except Exception as e:
            print(f"❌ Flight manifest creation failed: {e}")
            return

        # Test 1: Create a simple master waybill
        print("\n1. Testing master waybill creation...")
        test_awb_data = {
            'awb_number': 'TEST-123456',
            'type_code': '740',
            'manifest_id': 'FM-TEST-20250617',
            'origin_airport': 'NBO',
            'destination_airport': 'LLW',
            'total_pieces': 5,
            'total_weight': 100.0,
            'weight_unit': 'KGM',
            'gross_volume': 1.5,
            'volume_unit': 'MC',
            'summary_description': 'Test cargo',
            'special_handling_code': 'GEN',
            'status': 'PENDING',
            'is_placeholder': False,
            'in_transit': False,
            'total_pieces_expected': 5,
            'received_pieces_count': 0,
            'branch_id': 1,
        }

        try:
            awb_number, was_created = db_ops.create_or_update_master_waybill(test_awb_data, is_orphan=False)
            print(f"✅ Master waybill created: {awb_number}, was_created: {was_created}")
        except Exception as e:
            print(f"❌ Master waybill creation failed: {e}")
            import traceback
            traceback.print_exc()

        # Test 2: Create ULD allocation
        print("\n2. Testing ULD allocation creation...")
        try:
            allocation_data = {
                'uld_id': 'TEST-ULD-001',
                'manifest_id': 'FM-TEST-20250617',
                'awb_number': 'TEST-123456',
                'pieces': 5,
                'weight': 100.0,
                'volume': 1.5,
                'volume_unit': 'MC',
                'split_code': 'T',
                'description': 'Test cargo',
                'summary_description': 'Test cargo',
                'branch_id': 1,
                'created_by': 2,
                'updated_by': 2,
                'created_at': 'NOW()',
                'updated_at': 'NOW()'
            }

            db_ops.insert_record('uld_awbs', allocation_data)
            print("✅ ULD allocation created successfully")
        except Exception as e:
            print(f"❌ ULD allocation creation failed: {e}")
            import traceback
            traceback.print_exc()

        # Test 3: Create partial waybill
        print("\n3. Testing partial waybill creation...")
        try:
            partial_data = {
                'partial_id': 'PWB-TEST-123456-1',
                'master_awb_number': 'TEST-123456',
                'manifest_id': 'FM-TEST-20250617',
                'origin_airport': 'NBO',
                'destination_airport': 'LLW',
                'expected_pieces': 5,
                'expected_weight': 100.0,
                'received_pieces': 5,
                'received_weight': 100.0,
                'remaining_pieces': 0,
                'remaining_weight': 0,
                'split_code': 'P',
                'split_sequence': 1,
                'status': 'CHECKED_IN',
                'special_handling_code': 'GEN',
                'summary_description': 'Test cargo',
                'uld_id': 'TEST-ULD-001',
                'weight_unit': 'KGM',
                'gross_volume': 1.5,
                'volume_unit': 'MC',
                'source': 'XFFM',
                'branch_id': 1,
                'created_by': 2,
                'updated_by': 2,
                'created_at': 'NOW()',
                'updated_at': 'NOW()'
            }

            db_ops.insert_record('partial_waybills', partial_data)
            print("✅ Partial waybill created successfully")
        except Exception as e:
            print(f"❌ Partial waybill creation failed: {e}")
            import traceback
            traceback.print_exc()

        # Commit changes
        conn.commit()
        print("\n✅ All tests completed and committed")

        # Clean up test data
        print("\n4. Cleaning up test data...")
        try:
            cursor.execute("DELETE FROM uld_awbs WHERE awb_number = 'TEST-123456'")
            cursor.execute("DELETE FROM partial_waybills WHERE master_awb_number = 'TEST-123456'")
            cursor.execute("DELETE FROM master_waybills WHERE awb_number = 'TEST-123456'")
            cursor.execute("DELETE FROM flight_manifests WHERE manifest_id = 'FM-TEST-20250617'")
            conn.commit()
            print("✅ Test data cleaned up")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_operations()
