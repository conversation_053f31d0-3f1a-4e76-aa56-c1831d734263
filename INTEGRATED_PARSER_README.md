# Integrated Cargo XML Parser System

## Overview

The Integrated Cargo XML Parser System is a comprehensive solution for processing air cargo XML messages in compliance with IATA Cargo-XML standards. It handles three main message types:

- **XFWB** (Master Air Waybill) - Phase 1: Declaration and duplicate prevention
- **XFFM** (Flight Manifest) - Phase 2: Reconciliation and ULD processing
- **XFZB** (House Air Waybill) - Integration with master waybills

## Key Features

### 🔄 Two-Stage Processing Workflow
1. **Phase 1 (XFWB)**: Master waybill declarations with SHA-256 duplicate prevention
2. **Phase 2 (XFFM)**: Flight reconciliation with sophisticated split handling
3. **Integration (XFZB)**: House waybill processing and party management

### 🛡️ Advanced Data Integrity
- **Duplicate Prevention**: SHA-256 hashing prevents duplicate processing
- **Orphan Detection**: Automatic detection and eventual recovery of orphaned AWBs
- **Split Handling**: Comprehensive support for ULD splits (T, S, P, D codes)
- **In-Transit Logic**: Branch-aware processing for multi-location operations

### 📊 Comprehensive Tracking
- **Partial Shipments**: Automatic creation and tracking of partial waybills
- **ULD Management**: Complete ULD allocation and inventory tracking
- **Audit Trails**: Full logging and processing history
- **Performance Monitoring**: Built-in statistics and performance metrics

### 🔍 Intelligent Processing
- **Auto-Detection**: Automatic XML message type detection
- **Party Management**: Intelligent shipper/consignee matching and creation
- **Status Tracking**: Real-time AWB status updates and reconciliation
- **Error Recovery**: Robust error handling with detailed logging

## Installation

### Prerequisites
- Python 3.8+
- PostgreSQL database
- Required Python packages (see requirements.txt)

### Setup
1. **Install Dependencies**:
   ```bash
   cd /var/www/aircargomis/python/xml-parsers
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

2. **Configure Database**:
   - Copy `config/database.template.py` to `config/database.py`
   - Update database connection settings

3. **Verify Installation**:
   ```bash
   python3 integrated_cargo_parser.py --help
   ```

## Usage

### Command Line Interface

#### Process Single File
```bash
python3 integrated_cargo_parser.py /path/to/file.xml --verbose
```

#### Process Directory
```bash
python3 integrated_cargo_parser.py /path/to/directory/ --verbose
```

#### Directory Monitoring (Real-time)
```bash
python3 integrated_cargo_parser.py --monitor /path/to/watch/directory --verbose
```

#### Show Statistics
```bash
python3 integrated_cargo_parser.py --stats
```

### Command Line Options
- `--branch-id`: Branch ID for processing (default: 1)
- `--user-id`: User ID for audit trails (default: 1)
- `--verbose`: Enable detailed logging
- `--stats`: Show processing statistics
- `--monitor`: Monitor directory for new files

## Message Type Processing

### XFWB (Master Air Waybill)
**Purpose**: Initial AWB declaration and duplicate prevention

**Processing Logic**:
- SHA-256 hash calculation for duplicate detection
- Master waybill creation with party information
- Goods description and special handling extraction
- In-transit determination based on destination

**Key Features**:
- Duplicate prevention using file content hashing
- Automatic party (shipper/consignee) creation
- Comprehensive data validation
- Status tracking initialization

### XFFM (Flight Manifest)
**Purpose**: Flight reconciliation and ULD processing

**Processing Logic**:
- Flight manifest creation if not exists
- ULD record creation and management
- Split type handling (T, S, P, D)
- Partial waybill creation for split shipments

**Split Type Handling**:
- **T (Total)**: Complete shipment in ULD
- **S (Split)**: AWB split across multiple ULDs
- **P (Partial)**: Partial shipment (creates partial waybill)
- **D (Split+Partial)**: Both split and partial

**Key Features**:
- Sophisticated ULD allocation tracking
- Automatic partial waybill generation
- Master AWB reconciliation
- Orphan AWB creation for missing masters

### XFZB (House Air Waybill)
**Purpose**: House waybill integration

**Processing Logic**:
- House waybill creation linked to master AWB
- Party information extraction and matching
- Goods description processing
- Master AWB association

**Key Features**:
- Automatic master AWB linking
- Party information integration
- Comprehensive house waybill tracking

## Database Schema Integration

### Core Tables
- `master_waybills`: Primary AWB records
- `house_waybills`: House AWB records
- `partial_waybills`: Partial shipment tracking
- `flight_manifests`: Flight information
- `ulds`: ULD records and allocations
- `uld_awbs`: ULD-AWB allocation tracking

### Audit and Logging
- `processing_log`: Complete processing history
- `duplicate_files`: Duplicate detection tracking
- `parties`: Shipper/consignee information

## Performance and Monitoring

### Built-in Statistics
```bash
python3 integrated_cargo_parser.py --stats
```

Shows:
- Recent 24-hour processing activity
- Master waybill status breakdown
- Partial waybill statistics
- Processing performance metrics

### Logging Levels
- **INFO**: Standard processing information
- **DEBUG**: Detailed processing steps (use --verbose)
- **ERROR**: Error conditions and failures
- **WARNING**: Non-critical issues and alerts

## Error Handling and Recovery

### Duplicate Prevention
- SHA-256 content hashing prevents duplicate processing
- Duplicate files are logged but not reprocessed
- Hash-based detection works regardless of filename

### Orphan Recovery
- Orphan AWBs are automatically created for missing masters
- In-transit logic determines appropriate branch assignment
- Eventual reconciliation when master AWBs arrive

### Transaction Management
- Database transactions ensure data consistency
- Rollback on errors prevents partial data corruption
- Comprehensive error logging for troubleshooting

## Directory Monitoring

### Real-time Processing
The system supports real-time directory monitoring using the `--monitor` option:

```bash
python3 integrated_cargo_parser.py --monitor /incoming/xml/files --verbose
```

**Features**:
- Automatic detection of new XML files
- Immediate processing upon file creation
- Continuous monitoring until stopped (Ctrl+C)
- Full logging of all processing activities

**Use Cases**:
- Production environments with automated file drops
- Integration with external systems
- Real-time cargo processing workflows

## Best Practices

### File Organization
- Use separate directories for different message types
- Implement file naming conventions for easy identification
- Archive processed files to prevent reprocessing

### Performance Optimization
- Process files in chronological order when possible
- Use directory monitoring for real-time processing
- Monitor database performance and optimize queries

### Error Management
- Review processing logs regularly
- Monitor orphan AWB creation patterns
- Investigate duplicate detection alerts

### Data Quality
- Validate XML files before processing
- Monitor partial waybill creation rates
- Review in-transit logic accuracy

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify database configuration in `config/database.py`
   - Check PostgreSQL service status
   - Validate connection credentials

2. **XML Parsing Errors**
   - Verify XML file format and structure
   - Check for encoding issues
   - Validate against IATA Cargo-XML standards

3. **Duplicate Detection Issues**
   - Review file content for actual duplicates
   - Check SHA-256 hash calculation
   - Verify duplicate_files table entries

4. **Performance Issues**
   - Monitor database query performance
   - Check system resources (CPU, memory)
   - Review processing log for bottlenecks

### Support and Maintenance

For technical support and maintenance:
- Review processing logs in detail
- Use verbose mode for debugging
- Check database integrity regularly
- Monitor system performance metrics

## Integration Examples

### Batch Processing
```bash
# Process all XML files in a directory
python3 integrated_cargo_parser.py /data/xml/batch/ --verbose

# Show results
python3 integrated_cargo_parser.py --stats
```

### Real-time Monitoring
```bash
# Start monitoring (runs continuously)
python3 integrated_cargo_parser.py --monitor /data/xml/incoming/ --verbose
```

### Automated Workflows
```bash
#!/bin/bash
# Example automation script
WATCH_DIR="/data/xml/incoming"
LOG_FILE="/var/log/cargo_parser.log"

python3 integrated_cargo_parser.py --monitor "$WATCH_DIR" --verbose >> "$LOG_FILE" 2>&1
```

This integrated system provides a robust, scalable solution for air cargo XML processing with comprehensive error handling, performance monitoring, and real-time capabilities.
