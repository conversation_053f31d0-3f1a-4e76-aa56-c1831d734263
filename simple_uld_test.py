#!/usr/bin/env python3
"""
Simple test to verify ULD enhancement functionality.
"""

import os
import sys
from pathlib import Path

import psycopg2

# Add the parent directory to the path
sys.path.append("/var/www/cargo-mis")


def test_database_connection():
    """Test database connection."""
    try:
        from python.xml_parsers.config.database import DB_CONFIG

        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Test ULD-related tables
        tables = ["uld_details", "uld_awb_allocations", "uld_awbs", "uld_movements"]
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"✅ Table {table}: {count} records")

        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False


def test_xml_parsing():
    """Test XML parsing functionality."""
    try:
        from lxml import etree

        from python.xml_parsers.extractors.xffm_extractor import XFFMExtractor

        # Test file path
        test_file = "/var/www/cargo-mis/examples/XFFM/XFFM_EK9747_16-Apr-2025_fixed.xml"

        if not Path(test_file).exists():
            print(f"❌ Test file not found: {test_file}")
            return False

        # Initialize extractor
        extractor = XFFMExtractor()

        # Parse the XML file
        with open(test_file, "r", encoding="utf-8") as f:
            xml_content = f.read()

        # Parse XML string to element tree
        root_element = etree.fromstring(xml_content.encode("utf-8"))

        data = extractor.extract(root_element)

        print(f"✅ XML parsing successful")
        print(f"Flight info: {data.get('flight_info', {})}")
        print(f"ULDs found: {len(data.get('ulds', []))}")
        print(f"Waybills found: {len(data.get('waybills', []))}")

        # Show ULD details
        for i, uld in enumerate(data.get("ulds", [])):
            print(f"ULD {i+1}: {uld.get('uld_id')} - {len(uld.get('awbs', []))} AWBs")
            for j, awb in enumerate(uld.get("awbs", [])):
                print(
                    f"  AWB {j+1}: {awb.get('awb_number')} - {awb.get('pieces')} pieces"
                )

        return True

    except Exception as e:
        print(f"❌ XML parsing error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Simple ULD Enhancement Test")
    print("=" * 40)

    # Test database connection
    if not test_database_connection():
        sys.exit(1)

    # Test XML parsing
    if not test_xml_parsing():
        sys.exit(1)

    print("✅ All tests passed!")
