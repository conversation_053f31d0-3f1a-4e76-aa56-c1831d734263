# Quick Start Guide - Integrated Cargo XML Parser

## 🚀 Quick Setup

### 1. Install Dependencies
```bash
cd /var/www/aircargomis/python/xml-parsers
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 2. Configure Database
Copy and edit the database configuration:
```bash
cp config/database.template.py config/database.py
# Edit config/database.py with your database settings
```

### 3. Test Installation
```bash
python3 integrated_cargo_parser.py --help
```

## 📋 Common Usage Examples

### Process a Single XML File
```bash
python3 integrated_cargo_parser.py /path/to/file.xml --verbose
```

### Process All Files in a Directory
```bash
python3 integrated_cargo_parser.py /path/to/directory/ --verbose
```

### Monitor Directory for New Files (Real-time)
```bash
python3 integrated_cargo_parser.py --monitor /path/to/watch/directory --verbose
```

### Show Processing Statistics
```bash
python3 integrated_cargo_parser.py --stats
```

## 🔍 Message Types Supported

| Type | Description | Phase |
|------|-------------|-------|
| XFWB | Master Air Waybill | Phase 1 (Declaration) |
| XFFM | Flight Manifest | Phase 2 (Reconciliation) |
| XFZB | House Air Waybill | Integration |

## 📊 Processing Workflow

1. **XFWB Processing**: Creates master waybills with duplicate prevention
2. **XFFM Processing**: Reconciles with flight manifests, creates ULD allocations
3. **XFZB Processing**: Links house waybills to master waybills

## ⚡ Quick Test

Test with example files:
```bash
# Test XFWB processing
python3 integrated_cargo_parser.py /var/www/aircargomis/examples/XFWB-706-51722403.xml --verbose

# Test XFFM processing
python3 integrated_cargo_parser.py /var/www/aircargomis/examples/XFFM-KQ2738.xml --verbose

# Test XFZB processing
python3 integrated_cargo_parser.py /var/www/aircargomis/examples/XFZB-SHA123.xml --verbose
```

## 🛠️ Command Line Options

| Option | Description | Example |
|--------|-------------|---------|
| `--verbose` | Enable detailed logging | `--verbose` |
| `--stats` | Show processing statistics | `--stats` |
| `--monitor DIR` | Monitor directory for new files | `--monitor /incoming/` |
| `--branch-id ID` | Set branch ID (default: 1) | `--branch-id 2` |
| `--user-id ID` | Set user ID (default: 1) | `--user-id 5` |

## 🔧 Troubleshooting

### Database Connection Issues
1. Check database configuration in `config/database.py`
2. Verify PostgreSQL is running
3. Test connection credentials

### XML Processing Errors
1. Verify XML file format
2. Check file permissions
3. Use `--verbose` for detailed error information

### Performance Issues
1. Monitor system resources
2. Check database performance
3. Review processing logs

## 📈 Monitoring and Logs

### View Recent Activity
```bash
python3 integrated_cargo_parser.py --stats
```

### Enable Verbose Logging
```bash
python3 integrated_cargo_parser.py /path/to/file.xml --verbose
```

### Real-time Monitoring
```bash
# Monitor directory continuously
python3 integrated_cargo_parser.py --monitor /incoming/xml/ --verbose

# Stop with Ctrl+C
```

## 🎯 Production Usage

### Automated Processing
```bash
#!/bin/bash
# Production script example
WATCH_DIR="/data/xml/incoming"
LOG_FILE="/var/log/cargo_parser.log"

cd /var/www/aircargomis/python/xml-parsers
source venv/bin/activate
python3 integrated_cargo_parser.py --monitor "$WATCH_DIR" --verbose >> "$LOG_FILE" 2>&1
```

### Batch Processing
```bash
# Process all files in a directory
python3 integrated_cargo_parser.py /data/xml/batch/ --verbose

# Show results
python3 integrated_cargo_parser.py --stats
```

## 📚 Next Steps

1. Read the full documentation: `INTEGRATED_PARSER_README.md`
2. Review database schema documentation
3. Set up automated monitoring for production
4. Configure log rotation and monitoring
5. Test with your specific XML files

## 🆘 Getting Help

1. Use `--help` for command line options
2. Use `--verbose` for detailed processing information
3. Check processing logs for error details
4. Review the comprehensive README for advanced features

For more detailed information, see `INTEGRATED_PARSER_README.md`.
