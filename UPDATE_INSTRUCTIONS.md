# XML Parser Update Instructions

The XML parsers have been updated to properly handle JSONB fields and avoid updating sync fields. This document provides instructions on how to test the changes.

## Changes Made

1. **XFWB Parser** (`python/xml-parsers/xfwb/parser.py`):
   - Updated the `_save_waybill_to_db` method to properly handle JSONB fields
   - Added code to extract goods descriptions from XML
   - Added code to detect if a master waybill has house waybills
   - Added a new `_extract_type_code` method to correctly identify 741 (Master AWB with houses) vs 740 (Direct AWB without houses) type codes
   - Removed updates to `sync_status`, `sync_timestamp`, and `sync_client_id` fields

2. **XFZB Parser** (`python/xml-parsers/xfzb/parser.py`):
   - Updated the `_save_house_waybill_to_db` method to properly handle JSONB fields
   - Added code to extract goods descriptions from XML
   - Added code to update the master waybill to set `has_houses=TRUE`
   - Removed updates to `sync_status`, `sync_timestamp`, and `sync_client_id` fields

## Testing the Changes

To test the changes, you can use the sample XML files:

```bash
# Test the XFWB parser
python3 /var/www/cargo-handling-system/python/xml-parsers/process_xfwb.py /var/www/cargo-handling-system/examples/XFWB/XFWB_001-91055543.xml

# Check the database to verify the changes
psql -U postgres -d cargo_handling -c "SELECT awb_number, type_code, special_handling_code, special_handling_codes, goods_descriptions, has_houses FROM master_waybills WHERE awb_number = '001-91055543'"
```

Expected results:
- `type_code` should be set to `741` (for House AWB) instead of the hardcoded `740`
- `special_handling_codes` should be set to `["EAW"]`
- `goods_descriptions` should be set to `{"description": "CONSOLIDATION AS PER ATTACHED MANIFEST"}`
- `has_houses` should be set to `true`
- `sync_status`, `sync_timestamp`, and `sync_client_id` should remain unchanged

## Troubleshooting

If you encounter any issues:

1. Check the logs for error messages
2. Verify that the XML file contains the expected data
3. Make sure the database schema matches the expected structure

## Additional Notes

- The `manifest_id` is assigned by the XFFM parser, not by the XFWB or XFZB parsers
- The `sync_status`, `sync_timestamp`, and `sync_client_id` fields are managed by the SOAP API when MRA receives the data
- The `has_houses` flag is set based on the presence of `<ram:IncludedHeaderNote>` with `<ram:ContentCode>C</ram:ContentCode>` in the XML
