#!/usr/bin/env python3
"""
ONE Record Converter for XML Parsers.

This module converts parsed XML data to IATA ONE Record format (JSON-LD)
following the IATA ONE Record ontology standards.
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional


class OneRecordConverter:
    """Convert parsed XML data to IATA ONE Record format."""

    def __init__(self, ontology_version: str = "2.1"):
        """
        Initialize ONE Record converter.

        Args:
            ontology_version: ONE Record ontology version
        """
        self.ontology_version = ontology_version
        self.context_url = "https://onerecord.iata.org/ns/cargo#"
        self.logger = logging.getLogger(self.__class__.__name__)

    def convert_waybill_to_one_record(
        self, waybill_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Convert waybill data to ONE Record format.

        Args:
            waybill_data: Parsed waybill data

        Returns:
            ONE Record formatted waybill
        """
        try:
            self.logger.debug("Converting waybill to ONE Record format")

            # Determine waybill type
            waybill_type = self._map_waybill_type(waybill_data.get("type_code"))

            # Create base ONE Record waybill
            one_record_waybill = {
                "@context": self.context_url,
                "@type": "Waybill",
                "@id": f"waybill:{waybill_data.get('awb_number', '')}",
                "waybillPrefix": self._extract_awb_prefix(
                    waybill_data.get("awb_number")
                ),
                "waybillNumber": self._extract_awb_number(
                    waybill_data.get("awb_number")
                ),
                "waybillType": waybill_type,
                "originCurrency": waybill_data.get("currency_code"),
                "shipment": self._create_shipment_object(waybill_data),
                "booking": self._create_booking_object(waybill_data),
            }

            # Add house waybills if this is a master waybill
            if waybill_type == "MASTER" and waybill_data.get("house_waybills"):
                one_record_waybill["containedWaybills"] = self._create_house_waybills(
                    waybill_data["house_waybills"]
                )

            # Add validation metadata
            one_record_waybill["_metadata"] = {
                "convertedAt": datetime.now().isoformat(),
                "ontologyVersion": self.ontology_version,
                "sourceFormat": "Cargo-XML",
                "converter": "Air Cargo Malawi XML Parser",
            }

            self.logger.info(
                f"Successfully converted waybill {waybill_data.get('awb_number')} to ONE Record"
            )
            return one_record_waybill

        except Exception as e:
            self.logger.error(f"Error converting waybill to ONE Record: {e}")
            raise

    def _map_waybill_type(self, type_code: Optional[str]) -> str:
        """
        Map IATA type codes to ONE Record waybill types.

        Args:
            type_code: IATA type code

        Returns:
            ONE Record waybill type
        """
        mapping = {
            "740": "DIRECT",  # Direct waybill without houses
            "741": "MASTER",  # Master waybill with houses
            "703": "HOUSE",  # House waybill
        }
        return mapping.get(type_code, "DIRECT")

    def _extract_awb_prefix(self, awb_number: Optional[str]) -> str:
        """Extract AWB prefix (first 3 digits)."""
        if not awb_number:
            return ""

        clean_awb = awb_number.replace("-", "").replace(" ", "")
        return clean_awb[:3] if len(clean_awb) >= 3 else ""

    def _extract_awb_number(self, awb_number: Optional[str]) -> str:
        """Extract AWB number (last 8 digits)."""
        if not awb_number:
            return ""

        clean_awb = awb_number.replace("-", "").replace(" ", "")
        return clean_awb[3:] if len(clean_awb) >= 11 else clean_awb

    def _create_shipment_object(self, waybill_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create ONE Record Shipment object."""
        shipment = {
            "@type": "Shipment",
            "totalPieceCount": waybill_data.get("total_pieces"),
            "totalGrossWeight": self._create_value_object(
                waybill_data.get("total_weight"), waybill_data.get("weight_unit", "KGM")
            ),
            "goodsDescription": waybill_data.get("goods_description")
            or waybill_data.get("summary_description"),
            "containedPieces": [self._create_piece_object(waybill_data)],
        }

        # Add volume if available
        if waybill_data.get("gross_volume"):
            shipment["totalVolume"] = self._create_value_object(
                waybill_data.get("gross_volume"), waybill_data.get("volume_unit", "MC")
            )

        # Add special handling requirements
        if waybill_data.get("special_handling_codes"):
            shipment["specialHandlingCodes"] = waybill_data["special_handling_codes"]

        return shipment

    def _create_piece_object(self, waybill_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create ONE Record Piece object."""
        piece = {
            "@type": "Piece",
            "pieceCount": waybill_data.get("total_pieces", 1),
            "grossWeight": self._create_value_object(
                waybill_data.get("total_weight"), waybill_data.get("weight_unit", "KGM")
            ),
        }

        # Add dimensions if available
        if waybill_data.get("dimensions"):
            piece["dimensions"] = self._create_dimensions_object(
                waybill_data["dimensions"]
            )

        return piece

    def _create_value_object(self, value: Any, unit: str) -> Dict[str, Any]:
        """Create ONE Record Value object."""
        return {
            "@type": "Value",
            "value": float(value) if value is not None else 0.0,
            "unit": unit,
        }

    def _create_dimensions_object(self, dimensions: Dict[str, Any]) -> Dict[str, Any]:
        """Create ONE Record Dimensions object."""
        return {
            "@type": "Dimensions",
            "length": self._create_value_object(dimensions.get("length"), "CMT"),
            "width": self._create_value_object(dimensions.get("width"), "CMT"),
            "height": self._create_value_object(dimensions.get("height"), "CMT"),
        }

    def _create_booking_object(self, waybill_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create ONE Record Booking object."""
        booking = {
            "@type": "Booking",
            "bookingRequest": {
                "@type": "BookingRequest",
                "bookingOption": {
                    "@type": "BookingOption",
                    "parties": self._create_parties_list(waybill_data),
                    "bookingSegment": {
                        "@type": "BookingSegment",
                        "departureLocation": waybill_data.get("origin_airport"),
                        "arrivalLocation": waybill_data.get("destination_airport"),
                    },
                },
            },
        }

        # Add flight details if available
        if waybill_data.get("flight_details"):
            booking["bookingRequest"]["bookingOption"]["transportMovements"] = (
                self._create_transport_movements(waybill_data["flight_details"])
            )

        return booking

    def _create_parties_list(
        self, waybill_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Create list of parties in ONE Record format."""
        parties = []

        # Add shipper
        if waybill_data.get("shipper_name"):
            parties.append(
                {
                    "@type": "Party",
                    "partyRole": "SHP",
                    "partyDetails": {
                        "@type": "Company",
                        "name": waybill_data.get("shipper_name"),
                        "branch": {
                            "@type": "CompanyBranch",
                            "location": {
                                "@type": "Location",
                                "address": self._create_address_object(
                                    waybill_data, "shipper"
                                ),
                            },
                        },
                    },
                }
            )

        # Add consignee
        if waybill_data.get("consignee_name"):
            parties.append(
                {
                    "@type": "Party",
                    "partyRole": "CNE",
                    "partyDetails": {
                        "@type": "Company",
                        "name": waybill_data.get("consignee_name"),
                        "branch": {
                            "@type": "CompanyBranch",
                            "location": {
                                "@type": "Location",
                                "address": self._create_address_object(
                                    waybill_data, "consignee"
                                ),
                            },
                        },
                    },
                }
            )

        # Add agent/forwarder
        if waybill_data.get("agent_name"):
            parties.append(
                {
                    "@type": "Party",
                    "partyRole": "AGT",
                    "partyDetails": {
                        "@type": "Company",
                        "name": waybill_data.get("agent_name"),
                        "accountNumber": waybill_data.get("agent_account_id"),
                    },
                }
            )

        return parties

    def _create_address_object(
        self, waybill_data: Dict[str, Any], party_type: str
    ) -> Dict[str, Any]:
        """Create ONE Record Address object."""
        address = {"@type": "Address"}

        # Map party-specific address fields
        if party_type == "shipper":
            address.update(
                {
                    "streetAddress": waybill_data.get("shipper_address"),
                    "cityName": waybill_data.get("shipper_city"),
                    "countryCode": waybill_data.get("shipper_country"),
                    "postalCode": waybill_data.get("shipper_postal_code"),
                }
            )
        elif party_type == "consignee":
            address.update(
                {
                    "streetAddress": waybill_data.get("consignee_address"),
                    "cityName": waybill_data.get("consignee_city"),
                    "countryCode": waybill_data.get("consignee_country"),
                    "postalCode": waybill_data.get("consignee_postal_code"),
                }
            )

        # Remove None values
        return {k: v for k, v in address.items() if v is not None}

    def _create_transport_movements(
        self, flight_details: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Create ONE Record TransportMovement objects."""
        movements = []

        for flight in flight_details:
            movement = {
                "@type": "TransportMovement",
                "transportIdentifier": flight.get("flight_number"),
                "departureLocation": flight.get("departure_airport"),
                "arrivalLocation": flight.get("arrival_airport"),
                "scheduledDepartureDateTime": flight.get("departure_time"),
                "scheduledArrivalDateTime": flight.get("arrival_time"),
            }
            movements.append(movement)

        return movements

    def _create_house_waybills(
        self, house_waybills: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Create ONE Record house waybill objects."""
        house_waybill_objects = []

        for house_waybill in house_waybills:
            house_waybill_object = self.convert_waybill_to_one_record(house_waybill)
            house_waybill_objects.append(house_waybill_object)

        return house_waybill_objects

    def convert_to_json(self, one_record_data: Dict[str, Any], indent: int = 2) -> str:
        """
        Convert ONE Record data to JSON string.

        Args:
            one_record_data: ONE Record data dictionary
            indent: JSON indentation

        Returns:
            JSON string
        """
        try:
            return json.dumps(one_record_data, indent=indent, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Error converting to JSON: {e}")
            raise

    def validate_one_record_format(self, one_record_data: Dict[str, Any]) -> List[str]:
        """
        Validate ONE Record format compliance.

        Args:
            one_record_data: ONE Record data to validate

        Returns:
            List of validation issues
        """
        issues = []

        # Check required fields
        required_fields = ["@context", "@type", "@id"]
        for field in required_fields:
            if field not in one_record_data:
                issues.append(f"Missing required field: {field}")

        # Check context URL
        if one_record_data.get("@context") != self.context_url:
            issues.append(f"Invalid context URL: {one_record_data.get('@context')}")

        # Check type
        if one_record_data.get("@type") != "Waybill":
            issues.append(f"Invalid type: {one_record_data.get('@type')}")

        # Check waybill type
        valid_waybill_types = ["DIRECT", "MASTER", "HOUSE"]
        waybill_type = one_record_data.get("waybillType")
        if waybill_type and waybill_type not in valid_waybill_types:
            issues.append(f"Invalid waybill type: {waybill_type}")

        return issues
