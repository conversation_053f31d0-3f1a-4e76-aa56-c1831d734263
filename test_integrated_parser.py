#!/usr/bin/env python3
"""
Test script for the Integrated Cargo XML Parser System.

This script validates the integrated parser system using the example XML files
and demonstrates the two-stage processing workflow.
"""

import logging
import os
import sys
import time
from pathlib import Path

# Add the current directory to the path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from integrated_cargo_parser import IntegratedCargoParser


def setup_test_logging():
    """Setup logging for tests."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_integrated_parser.log')
        ]
    )
    return logging.getLogger('TestIntegratedParser')


def test_single_file_processing(parser, file_path, expected_type):
    """
    Test processing of a single XML file.

    Args:
        parser: IntegratedCargoParser instance
        file_path (str): Path to XML file
        expected_type (str): Expected message type (XFWB, XFFM, XFZB)

    Returns:
        dict: Test result
    """
    logger = logging.getLogger('TestIntegratedParser')
    
    test_result = {
        'file': os.path.basename(file_path),
        'expected_type': expected_type,
        'success': False,
        'processing_result': None,
        'errors': []
    }

    try:
        logger.info(f"Testing {expected_type} file: {file_path}")
        
        if not os.path.exists(file_path):
            test_result['errors'].append(f"File not found: {file_path}")
            return test_result

        # Process the file
        start_time = time.time()
        result = parser.process_file(file_path)
        processing_time = time.time() - start_time

        test_result['processing_result'] = result
        test_result['processing_time'] = processing_time

        # Validate result
        if result.get('success'):
            test_result['success'] = True
            logger.info(f"✓ Successfully processed {expected_type} file in {processing_time:.2f}s")
            logger.info(f"  - AWBs: {result.get('awb_count', 0)}")
            logger.info(f"  - Partials: {result.get('partial_count', 0)}")
            if result.get('warnings'):
                logger.warning(f"  - Warnings: {result['warnings']}")
        else:
            test_result['errors'].extend(result.get('errors', []))
            logger.error(f"✗ Failed to process {expected_type} file")
            for error in result.get('errors', []):
                logger.error(f"  - {error}")

    except Exception as e:
        test_result['errors'].append(f"Exception during processing: {str(e)}")
        logger.error(f"✗ Exception processing {expected_type} file: {str(e)}", exc_info=True)

    return test_result


def test_workflow_sequence(parser, examples_dir):
    """
    Test the complete workflow sequence: XFWB -> XFFM -> XFZB.

    Args:
        parser: IntegratedCargoParser instance
        examples_dir (str): Path to examples directory

    Returns:
        dict: Workflow test result
    """
    logger = logging.getLogger('TestIntegratedParser')
    
    workflow_result = {
        'success': False,
        'phases': [],
        'errors': [],
        'statistics': {}
    }

    try:
        logger.info("=== Testing Complete Workflow Sequence ===")

        # Phase 1: Process XFWB files (Master Waybill Declarations)
        logger.info("Phase 1: Processing XFWB files...")
        xfwb_files = list(Path(examples_dir).glob("XFWB*.xml"))
        
        phase1_results = []
        for xfwb_file in xfwb_files:
            result = test_single_file_processing(parser, str(xfwb_file), 'XFWB')
            phase1_results.append(result)

        workflow_result['phases'].append({
            'phase': 'XFWB_DECLARATION',
            'files_processed': len(phase1_results),
            'successful': sum(1 for r in phase1_results if r['success']),
            'results': phase1_results
        })

        # Phase 2: Process XFFM files (Flight Reconciliation)
        logger.info("Phase 2: Processing XFFM files...")
        xffm_files = list(Path(examples_dir).glob("XFFM*.xml"))
        
        phase2_results = []
        for xffm_file in xffm_files:
            result = test_single_file_processing(parser, str(xffm_file), 'XFFM')
            phase2_results.append(result)

        workflow_result['phases'].append({
            'phase': 'XFFM_RECONCILIATION',
            'files_processed': len(phase2_results),
            'successful': sum(1 for r in phase2_results if r['success']),
            'results': phase2_results
        })

        # Phase 3: Process XFZB files (House Waybill Integration)
        logger.info("Phase 3: Processing XFZB files...")
        xfzb_files = list(Path(examples_dir).glob("XFZB*.xml"))
        
        phase3_results = []
        for xfzb_file in xfzb_files:
            result = test_single_file_processing(parser, str(xfzb_file), 'XFZB')
            phase3_results.append(result)

        workflow_result['phases'].append({
            'phase': 'XFZB_INTEGRATION',
            'files_processed': len(phase3_results),
            'successful': sum(1 for r in phase3_results if r['success']),
            'results': phase3_results
        })

        # Get final statistics
        stats = parser.get_processing_statistics()
        workflow_result['statistics'] = stats

        # Determine overall success
        total_files = sum(phase['files_processed'] for phase in workflow_result['phases'])
        total_successful = sum(phase['successful'] for phase in workflow_result['phases'])
        workflow_result['success'] = total_successful == total_files

        logger.info(f"Workflow complete: {total_successful}/{total_files} files processed successfully")

    except Exception as e:
        workflow_result['errors'].append(f"Workflow exception: {str(e)}")
        logger.error(f"Workflow exception: {str(e)}", exc_info=True)

    return workflow_result


def test_duplicate_prevention(parser, examples_dir):
    """
    Test duplicate prevention by processing the same file twice.

    Args:
        parser: IntegratedCargoParser instance
        examples_dir (str): Path to examples directory

    Returns:
        dict: Duplicate prevention test result
    """
    logger = logging.getLogger('TestIntegratedParser')
    
    test_result = {
        'success': False,
        'first_processing': None,
        'second_processing': None,
        'duplicate_detected': False,
        'errors': []
    }

    try:
        logger.info("=== Testing Duplicate Prevention ===")

        # Find an XFWB file to test with
        xfwb_files = list(Path(examples_dir).glob("XFWB*.xml"))
        if not xfwb_files:
            test_result['errors'].append("No XFWB files found for duplicate testing")
            return test_result

        test_file = str(xfwb_files[0])
        logger.info(f"Testing duplicate prevention with: {os.path.basename(test_file)}")

        # First processing
        logger.info("First processing...")
        result1 = parser.process_file(test_file)
        test_result['first_processing'] = result1

        # Second processing (should detect duplicate)
        logger.info("Second processing (should detect duplicate)...")
        result2 = parser.process_file(test_file)
        test_result['second_processing'] = result2

        # Check if duplicate was detected
        if result2.get('success') and result2.get('skipped_awbs'):
            test_result['duplicate_detected'] = True
            test_result['success'] = True
            logger.info("✓ Duplicate prevention working correctly")
        elif result2.get('warnings'):
            # Check warnings for duplicate messages
            duplicate_warnings = [w for w in result2['warnings'] if 'duplicate' in w.lower() or 'skipped' in w.lower()]
            if duplicate_warnings:
                test_result['duplicate_detected'] = True
                test_result['success'] = True
                logger.info("✓ Duplicate prevention working correctly (via warnings)")
            else:
                test_result['errors'].append("Duplicate not detected in warnings")
        else:
            test_result['errors'].append("Duplicate processing should have been prevented")

    except Exception as e:
        test_result['errors'].append(f"Exception during duplicate testing: {str(e)}")
        logger.error(f"Exception during duplicate testing: {str(e)}", exc_info=True)

    return test_result


def print_test_summary(workflow_result, duplicate_result):
    """Print a summary of all test results."""
    print("\n" + "="*60)
    print("INTEGRATED CARGO PARSER TEST SUMMARY")
    print("="*60)

    # Workflow results
    print(f"\n📋 WORKFLOW TEST:")
    print(f"   Overall Success: {'✓' if workflow_result['success'] else '✗'}")
    
    for phase in workflow_result['phases']:
        success_rate = f"{phase['successful']}/{phase['files_processed']}"
        status = "✓" if phase['successful'] == phase['files_processed'] else "✗"
        print(f"   {phase['phase']}: {status} ({success_rate})")

    # Statistics
    if workflow_result.get('statistics'):
        stats = workflow_result['statistics']
        print(f"\n📊 PROCESSING STATISTICS:")
        if 'master_waybills' in stats:
            mw_stats = stats['master_waybills']
            print(f"   Master AWBs: {mw_stats.get('total_master_awbs', 0)}")
            print(f"   - Expected: {mw_stats.get('expected_awbs', 0)}")
            print(f"   - In Progress: {mw_stats.get('in_progress_awbs', 0)}")
            print(f"   - Complete: {mw_stats.get('complete_awbs', 0)}")
            print(f"   - Orphans: {mw_stats.get('orphan_awbs', 0)}")
            print(f"   - In Transit: {mw_stats.get('in_transit_awbs', 0)}")

    # Duplicate prevention
    print(f"\n🔒 DUPLICATE PREVENTION:")
    print(f"   Success: {'✓' if duplicate_result['success'] else '✗'}")
    print(f"   Duplicate Detected: {'✓' if duplicate_result['duplicate_detected'] else '✗'}")

    # Overall assessment
    overall_success = workflow_result['success'] and duplicate_result['success']
    print(f"\n🎯 OVERALL TEST RESULT: {'✓ PASS' if overall_success else '✗ FAIL'}")
    
    if not overall_success:
        print("\n❌ ERRORS:")
        for error in workflow_result.get('errors', []):
            print(f"   - {error}")
        for error in duplicate_result.get('errors', []):
            print(f"   - {error}")

    print("="*60)


def main():
    """Main test function."""
    logger = setup_test_logging()
    
    # Configuration
    examples_dir = "/var/www/aircargomis/examples"
    branch_id = 1
    user_id = 1

    logger.info("Starting Integrated Cargo Parser Tests")
    logger.info(f"Examples directory: {examples_dir}")

    try:
        with IntegratedCargoParser(branch_id, user_id, logging.INFO) as parser:
            
            # Test complete workflow
            workflow_result = test_workflow_sequence(parser, examples_dir)
            
            # Test duplicate prevention
            duplicate_result = test_duplicate_prevention(parser, examples_dir)
            
            # Print summary
            print_test_summary(workflow_result, duplicate_result)

    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}", exc_info=True)
        print(f"❌ Test execution failed: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
