#!/usr/bin/env python3
"""
Debug aggregation logic for AWB 706-51764053.
"""

import sys
import psycopg2
from pathlib import Path
from lxml import etree

# Add the parent directory to the path
sys.path.append('/var/www/cargo-mis')

def debug_awb_aggregation():
    """Debug the aggregation process for AWB 706-51764053."""
    try:
        from python.xml_parsers.extractors.xffm_extractor import XFFMExtractor
        from python.xml_parsers.config.database import DB_CONFIG
        
        print("=== AWB 706-51764053 Aggregation Debug ===")
        print()
        
        # Parse the XFFM file to see raw data
        test_file = "/var/www/cargo-mis/examples/XFFM/XFFM_EK9747_16-Apr-2025_fixed.xml"
        
        if not Path(test_file).exists():
            print(f"❌ Test file not found: {test_file}")
            return False
        
        # Initialize extractor
        extractor = XFFMExtractor()
        
        # Parse the XML file
        with open(test_file, "r", encoding="utf-8") as f:
            xml_content = f.read()
        
        # Parse XML string to element tree
        root_element = etree.fromstring(xml_content.encode("utf-8"))
        
        # Extract data step by step
        print("1. Extracting ULD info...")
        uld_data = extractor.extract_uld_info(root_element)
        
        print("2. Extracting waybill info...")
        waybill_data = extractor.extract_waybill_info(root_element)
        
        print("3. Raw ULD data for AWB 706-51764053:")
        target_awb = "706-51764053"
        
        for i, uld in enumerate(uld_data.get("ulds", [])):
            uld_id = uld.get("uld_id")
            for j, awb in enumerate(uld.get("awbs", [])):
                if awb.get("awb_number") == target_awb:
                    print(f"   ULD {uld_id}: {awb.get('pieces')} pieces, {awb.get('weight')} kg")
                    print(f"   Split Code: {awb.get('transport_split_code')}")
                    print(f"   Is Split: {awb.get('is_split_across_ulds')}")
        
        print("4. Raw standalone waybill data for AWB 706-51764053:")
        for awb in waybill_data.get("waybills", []):
            if awb.get("awb_number") == target_awb:
                print(f"   Standalone: {awb.get('pieces')} pieces, {awb.get('weight')} kg")
                print(f"   Split Code: {awb.get('transport_split_code')}")
        
        print("5. Running aggregation...")
        aggregated_waybills = extractor.aggregate_awb_data(
            uld_data.get("ulds", []), waybill_data.get("waybills", [])
        )
        
        print("6. Aggregated result for AWB 706-51764053:")
        for awb in aggregated_waybills:
            if awb.get("awb_number") == target_awb:
                print(f"   Final: {awb.get('pieces')} pieces, {awb.get('weight')} kg")
                print(f"   Split Code: {awb.get('transport_split_code')}")
                print(f"   Is Split: {awb.get('is_split_across_ulds')}")
                print(f"   Is Partial: {awb.get('is_partial')}")
                break
        else:
            print("   ❌ AWB not found in aggregated results!")
        
        print()
        print("7. Database verification:")
        
        # Check database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # Master waybill
        cursor.execute("""
            SELECT total_pieces, total_weight, xml_data 
            FROM master_waybills 
            WHERE awb_number = %s
        """, (target_awb,))
        
        result = cursor.fetchone()
        if result:
            total_pieces, total_weight, xml_data = result
            print(f"   Master Waybill: {total_pieces} pieces, {total_weight} kg")
            if xml_data and 'transport_split_code' in xml_data:
                print(f"   Split Code in DB: {xml_data.get('transport_split_code')}")
        else:
            print("   ❌ Master waybill not found in database!")
        
        # ULD allocations
        cursor.execute("""
            SELECT uld_id, assigned_pieces, assigned_weight 
            FROM uld_awb_allocations 
            WHERE awb_number = %s
            ORDER BY uld_id
        """, (target_awb,))
        
        allocations = cursor.fetchall()
        total_allocated_pieces = 0
        total_allocated_weight = 0
        
        print("   ULD Allocations:")
        for uld_id, pieces, weight in allocations:
            print(f"     ULD {uld_id}: {pieces} pieces, {weight} kg")
            total_allocated_pieces += pieces
            total_allocated_weight += weight
        
        print(f"   Total from ULDs: {total_allocated_pieces} pieces, {total_allocated_weight} kg")
        
        # Check if totals match
        if result:
            if total_pieces == total_allocated_pieces and abs(total_weight - total_allocated_weight) < 0.01:
                print("   ✅ Master waybill totals match ULD allocations!")
            else:
                print("   ❌ Master waybill totals DO NOT match ULD allocations!")
                print(f"      Master: {total_pieces} pieces, {total_weight} kg")
                print(f"      ULDs:   {total_allocated_pieces} pieces, {total_allocated_weight} kg")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_awb_aggregation()
