# AWB Number Extraction Fix

## Overview

This update addresses the issue with XFWB files failing to import due to AWB numbers not being found. The changes include:

1. Enhanced AWB number extraction from XFWB XML files
2. Additional utility functions for AWB number validation and extraction
3. Fallback mechanisms to extract AWB numbers from non-standard locations
4. Improved error messages and debugging information
5. Test cases to verify AWB extraction functionality

## Key Changes

### 1. Enhanced AWB Number Extraction

The `extract_awb_number` method in `XFWBExtractor` has been updated to check for AWB numbers in multiple locations within the XML structure:

- MessageHeaderDocument/ID
- BusinessHeaderDocument/ID
- TransportContractDocument/ID
- MasterConsignment/ID
- AssociatedConsignmentItem/ID
- MainCarriageTransportMovement/AssociatedTransportDocument/ID
- IncludedHouseConsignment/ID
- AssociatedMasterConsignment/ID

### 2. New Utility Functions

New utility functions have been added to `awb_utils.py`:

- `is_valid_awb_format`: Validates if a string is in a valid AWB number format
- `extract_awb_from_text`: Extracts AWB numbers from text using regex patterns

### 3. Fallback Extraction

If the standard XPath-based extraction fails, the parser now attempts to extract AWB numbers from the raw XML text as a last resort.

### 4. Improved Error Handling

More detailed error messages and debugging information are now provided when AWB extraction fails, including:

- Logging the XML structure
- Identifying potential ID elements
- Providing specific error messages

### 5. Test Cases

A new test file `test_awb_extraction.py` has been added to verify the AWB extraction functionality.

## Running the Tests

To run the AWB extraction tests:

```bash
cd /var/www/aircargomis/python/xml-parsers
python -m tests.test_awb_extraction
```

## Troubleshooting

If you still encounter issues with AWB number extraction:

1. Check the logs for detailed error messages
2. Verify that the XFWB XML file contains a valid AWB number in one of the expected locations
3. If the AWB number is in a non-standard location, consider adding that XPath to the `extract_awb_number` method in `XFWBExtractor`

## Future Improvements

- Consider adding a configuration file to specify custom XPath expressions for AWB extraction
- Implement machine learning-based extraction for highly non-standard formats
- Add support for additional IATA Cargo-XML message types