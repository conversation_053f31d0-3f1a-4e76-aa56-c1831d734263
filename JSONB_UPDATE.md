# XML Parser Updates for JSONB Fields

This document contains update instructions for the XFWB and XFZB parsers to properly handle the JSONB fields in the database.

## Background

The database schema has been updated to include JSONB fields for `goods_descriptions` and `special_handling_codes`, but the XML parsers are not currently using these fields. Additionally, there are new fields like `has_houses` for master waybills that need to be properly set.

## Issues Identified

1. **JSONB Fields Not Being Populated**:
   - The parsers are extracting the data correctly but not storing it in the JSONB fields
   - The SQL queries need to be modified to properly format and store the data
   - For example, an AWB with `special_handling_code` set to "EAW" has empty JSONB fields `goods_descriptions` and `special_handling_codes`

2. **Sync Fields Being Updated**:
   - The parsers are currently setting `sync_status`, `sync_timestamp`, and `sync_client_id`
   - These fields should be left alone as they are managed by the SOAP API when MRA receives the data

3. **Missing Goods Descriptions**:
   - The parsers are not consistently extracting goods descriptions from the XML
   - For example, descriptions in `<ram:NatureIdentificationTransportCargo>` elements are not being captured

4. **Incorrect Has Houses Flag**:
   - The `has_houses` flag is not being set correctly for AWBs with house waybills
   - AWBs with `<ram:IncludedHeaderNote>` containing `<ram:ContentCode>C</ram:ContentCode>` should have `has_houses` set to `true`

## Updates

### XFWB Parser Updates

The `xfwb/parser_update.py` file contains updates for the XFWB parser:

1. Updated SQL queries to include the JSONB fields and remove sync fields
2. Added code to properly format and store Handling Instructions codes and goods descriptions:
   - Converting string values to proper JSONB format
   - Handling both array and string inputs
   - Extracting goods descriptions from `<ram:NatureIdentificationTransportCargo>` elements when not available elsewhere
3. Added code to detect if a master waybill has house waybills and set the `has_houses` flag accordingly, including:
   - Checking for `IncludedHouseConsignment` elements
   - Checking for `ConsolidatedConsignmentIndicator` elements
   - **Checking for `<ram:IncludedHeaderNote>` with `<ram:ContentCode>C</ram:ContentCode>` in the `<rsm:BusinessHeaderDocument>` section**, which is a specific indicator that the AWB has house waybills

### XFZB Parser Updates

The `xfzb/parser_update.py` file contains updates for the XFZB parser:

1. Updated SQL queries to include the JSONB fields and remove sync fields
2. Added code to properly format and store Handling Instructions codes and goods descriptions:
   - Converting string values to proper JSONB format
   - Handling both array and string inputs
   - Extracting goods descriptions from various XML elements including `<ram:NatureIdentificationTransportCargo>` and `<ram:IncludedHouseConsignmentItem>`
3. Added code to update the master waybill to set `has_houses=TRUE` when a house waybill is created

> **Important Notes**:
> 1. The `manifest_id` is assigned by the XFFM parser, not by the XFWB or XFZB parsers. Therefore, we do not attempt to set or update the `manifest_id` in these parsers.
> 2. The `sync_status`, `sync_timestamp`, and `sync_client_id` fields should not be updated by the XML parsers as they are managed by the SOAP API when MRA receives the data.

## Implementation

To implement these updates:

1. Open the `xfwb/parser.py` file and update the `_save_waybill_to_db` method with the new SQL queries from `xfwb/parser_update.py`
2. Add the code to detect if a master waybill has house waybills to the `_extract_waybill_data` method

3. Open the `xfzb/parser.py` file and update the `_save_house_waybill_to_db` method with the new SQL queries from `xfzb/parser_update.py`
4. Add the code to update the master waybill to the `_ensure_master_waybill_exists` method

## Testing

After implementing these updates, test the parsers with sample XML files to ensure they correctly handle the JSONB fields and set the new fields properly.

Use the `test_parsers.py` script to test the parsers with sample XML files:

```bash
python test_parsers.py --xfwb ../examples/sample_xml/xfwb_sample.xml
python test_parsers.py --xfzb ../examples/sample_xml/xfzb_sample.xml
```

Check the database to ensure the JSONB fields are properly populated and the new fields are correctly set.
